// lib/screens/knowledge_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/knowledge_article.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/screens/knowledge_article_detail_screen.dart';
import 'package:fpo_traders/services/knowledge_service.dart';
import 'package:fpo_traders/utils/image_debug_tool.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class KnowledgeScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const KnowledgeScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<KnowledgeScreen> createState() => _KnowledgeScreenState();
}

class _KnowledgeScreenState extends State<KnowledgeScreen> {
  final ScrollController _scrollController = ScrollController();

  List<KnowledgeArticle> _articles = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchArticles();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          !_isLoadingMore &&
          _hasMorePages) {
        _loadMoreArticles();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Show logout confirmation dialog
  void _showLogoutDialog(BuildContext context, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(localizations.logout),
              onPressed: () {
                _logout(context);
              },
            ),
          ],
        );
      },
    );
  }

  // Perform logout action
  void _logout(BuildContext context) async {
    // Clear authentication token and other user data
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');

    // Navigate to login screen and clear the navigation stack
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _fetchArticles({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _articles = [];
      });
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final result = await KnowledgeService.getArticles(
        page: _currentPage,
        limit: 10,
      );

      final articles = result['articles'] as List<KnowledgeArticle>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        if (refresh) {
          _articles = articles;
        } else {
          _articles.addAll(articles);
        }

        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreArticles() async {
    if (!_hasMorePages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final result = await KnowledgeService.getArticles(
        page: _currentPage,
        limit: 10,
      );

      final articles = result['articles'] as List<KnowledgeArticle>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _articles.addAll(articles);
        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        // We don't set hasError here to prevent disrupting the existing list
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load more articles')));
      });
    }
  }

  Future<void> _refreshArticles() {
    return _fetchArticles(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (widget.isContentOnly) {
      return _buildBody(localizations);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.knowledge),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            tooltip: localizations.logout,
            onPressed: () => _showLogoutDialog(context, localizations),
          ),
        ],
      ),
      body: _buildBody(localizations),
      // Use the reusable bottom navigation widget
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 1, // Knowledge screen is at index 1
        user: widget.user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading && _articles.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError && _articles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              'Failed to load articles',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshArticles,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_articles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.menu_book_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No articles available',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Check back later for farming tips and news',
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshArticles,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshArticles,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16),
        itemCount: _articles.length + (_hasMorePages ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _articles.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final article = _articles[index];
          return _buildArticleCard(article);
        },
      ),
    );
  }

  Widget _buildArticleCard(KnowledgeArticle article) {
    final DateTime createdAt = DateTime.parse(article.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      KnowledgeArticleDetailScreen(articleId: article.id),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Article image
            if (article.imageUrl != null)
              GestureDetector(
                onLongPress: () {
                  // Launch image debug tool on long press (only in development)
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              ImageDebugScreen(imageUrl: article.imageUrl!),
                    ),
                  );
                },
                child: Container(
                  height: 160,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Center(child: CircularProgressIndicator()),
                      ),
                      Positioned.fill(
                        child: Image.network(
                          article.imageUrl!,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(); // The progress indicator is already in the stack
                          },
                          errorBuilder: (context, error, stackTrace) {
                            print(
                              'Error loading image in card: $error, URL: ${article.imageUrl}',
                            );
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.image,
                                    color: Colors.grey[500],
                                    size: 48,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Long press to debug',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    article.title,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8),

                  // Description
                  Text(
                    article.description,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey[700], fontSize: 14),
                  ),
                  SizedBox(height: 16),

                  // Metadata row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Author and date
                      Row(
                        children: [
                          Icon(Icons.person, size: 16, color: Colors.grey[600]),
                          SizedBox(width: 4),
                          Text(
                            article.createdByName,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          SizedBox(width: 8),
                          Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 4),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),

                      // Stats
                      Row(
                        children: [
                          Icon(
                            Icons.favorite,
                            size: 16,
                            color:
                                article.isLiked ? Colors.red : Colors.grey[400],
                          ),
                          SizedBox(width: 4),
                          Text(
                            article.likeCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          SizedBox(width: 8),
                          Icon(
                            Icons.visibility,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 4),
                          Text(
                            article.viewCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
