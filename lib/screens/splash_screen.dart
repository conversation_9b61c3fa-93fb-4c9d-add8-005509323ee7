import 'package:flutter/material.dart';
import 'dart:async';
import 'package:fpo_traders/services/api_service.dart';
import 'package:fpo_traders/screens/main_container_screen.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:provider/provider.dart';
import 'package:fpo_traders/providers/language_provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Delay slightly to ensure context is available
    Timer(Duration(milliseconds: 100), () {
      _checkAuthentication();
    });
  }

  Future<void> _checkAuthentication() async {
    final token = await ApiService.getToken();
    final languageProvider = Provider.of<LanguageProvider>(
      context,
      listen: false,
    );
    final hasSelectedLanguage = languageProvider.locale != null;

    if (token != null) {
      try {
        final response = await ApiService.authenticatedRequest(
          '/mobile/profile',
          'GET',
        );

        if (response['status'] == 'success') {
          final userData = response['data']['farmer'];
          final user = User.fromJson(userData);

          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => MainContainerScreen(user: user),
            ),
          );
          return;
        }
      } catch (e) {
        await ApiService.clearUserData();
      }
    }

    // After a slight delay for splash effect
    Timer(const Duration(seconds: 2), () {
      // If language is already selected, go to login screen
      // Otherwise go to language selection
      if (hasSelectedLanguage) {
        Navigator.of(context).pushReplacementNamed('/login');
      } else {
        Navigator.of(context).pushReplacementNamed('/language');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF7CB342),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/logo.png',
              width: 150,
              height: 150,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.agriculture,
                    size: 80,
                    color: const Color(0xFF7CB342),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            const Text(
              'FPO TRADERS',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
