// lib/screens/market_price_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/market_price.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/market_price_service.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/widgets/price_history_chart.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MarketPriceScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const MarketPriceScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<MarketPriceScreen> createState() => _MarketPriceScreenState();
}

class _MarketPriceScreenState extends State<MarketPriceScreen> {
  final ScrollController _scrollController = ScrollController();

  List<MarketPrice> _prices = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchPrices();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          !_isLoadingMore &&
          _hasMorePages) {
        _loadMorePrices();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _showLogoutDialog(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(localizations.logout),
              onPressed: () {
                _logout(context);
              },
            ),
          ],
        );
      },
    );
  }

  void _logout(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _fetchPrices({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _prices = [];
      });
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final result = await MarketPriceService.getLatestPrices(
        page: _currentPage,
        limit: 20,
      );

      final prices = result['prices'] as List<MarketPrice>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        if (refresh) {
          _prices = prices;
        } else {
          _prices.addAll(prices);
        }

        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMorePrices() async {
    if (!_hasMorePages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final result = await MarketPriceService.getLatestPrices(
        page: _currentPage,
        limit: 20,
      );

      final prices = result['prices'] as List<MarketPrice>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _prices.addAll(prices);
        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load more prices')));
      });
    }
  }

  Future<void> _refreshPrices() {
    return _fetchPrices(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (widget.isContentOnly) {
      return _buildBody(localizations);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.marketPrices),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            tooltip: localizations.logout,
            onPressed: () => _showLogoutDialog(context, localizations),
          ),
        ],
      ),
      body: _buildBody(localizations),
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 4, // Assuming market prices is at index 4
        user: widget.user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading && _prices.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError && _prices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              localizations.failedToLoadMarketPrices,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshPrices,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_prices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assessment_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              localizations.noMarketPricesAvailable,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              localizations.checkBackLater,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshPrices,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshPrices,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16),
        itemCount: _prices.length + (_hasMorePages ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _prices.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final price = _prices[index];
          return _buildPriceCard(price);
        },
      ),
    );
  }

  Widget _buildPriceCard(MarketPrice price) {
    final localizations = AppLocalizations.of(context)!;

    // Format the date
    final DateTime priceDate = DateTime.parse(price.priceDate);
    final String formattedDate = DateFormat.yMMMd().format(priceDate);

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Navigate to price detail or chart (to be implemented)
          _showPriceDetail(price);
        },
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              // Crop image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child:
                    price.cropImageUrl != null
                        ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            price.cropImageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.grass,
                                size: 30,
                                color: Colors.green[400],
                              );
                            },
                          ),
                        )
                        : Icon(Icons.grass, size: 30, color: Colors.green[400]),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      price.cropName,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        _buildPriceBox(
                          localizations.minPrice,
                          price.minMonthPrice,
                          price.unitType,
                        ),
                        SizedBox(width: 8),
                        _buildPriceBox(
                          localizations.avgPrice,
                          price.avgMonthPrice,
                          price.unitType,
                        ),
                        SizedBox(width: 8),
                        _buildPriceBox(
                          localizations.maxPrice,
                          price.maxMonthPrice,
                          price.unitType,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${price.pricePerUnit.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  Text(
                    '${localizations.per} ${price.unitType}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  SizedBox(height: 12),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPriceChangeColor(
                        price.pricePerUnit,
                        price.avgMonthPrice,
                      ).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getPriceChangeIcon(
                            price.pricePerUnit,
                            price.avgMonthPrice,
                          ),
                          size: 14,
                          color: _getPriceChangeColor(
                            price.pricePerUnit,
                            price.avgMonthPrice,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          '${_calculatePriceChange(price.pricePerUnit, price.avgMonthPrice).toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 12,
                            color: _getPriceChangeColor(
                              price.pricePerUnit,
                              price.avgMonthPrice,
                            ),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceBox(String label, double price, String unit) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
            Text(
              '₹${price.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPriceChangeColor(double currentPrice, double avgPrice) {
    if (avgPrice == 0) return Colors.grey;
    final change = ((currentPrice - avgPrice) / avgPrice) * 100;
    if (change > 0) return Colors.green;
    if (change < 0) return Colors.red;
    return Colors.grey;
  }

  IconData _getPriceChangeIcon(double currentPrice, double avgPrice) {
    if (avgPrice == 0) return Icons.remove;
    final change = ((currentPrice - avgPrice) / avgPrice) * 100;
    if (change > 0) return Icons.arrow_upward;
    if (change < 0) return Icons.arrow_downward;
    return Icons.remove;
  }

  double _calculatePriceChange(double currentPrice, double avgPrice) {
    if (avgPrice == 0) return 0;
    return ((currentPrice - avgPrice) / avgPrice) * 100;
  }

  void _showPriceDetail(MarketPrice price) {
    final localizations = AppLocalizations.of(context)!;

    // We'll implement a bottom sheet with price details and chart
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(16),
          height: MediaQuery.of(context).size.height * 0.7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  // Crop image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:
                        price.cropImageUrl != null
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                price.cropImageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.grass,
                                    size: 30,
                                    color: Colors.green[400],
                                  );
                                },
                              ),
                            )
                            : Icon(
                              Icons.grass,
                              size: 30,
                              color: Colors.green[400],
                            ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          price.cropName,
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          localizations.currentMarketPrice,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '₹${price.pricePerUnit.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      Text(
                        '${localizations.per} ${price.unitType}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 24),
              Text(
                localizations.lastThirtyDaysPriceRange,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildPriceStatCard(
                      localizations.minPrice,
                      '₹${price.minMonthPrice.toStringAsFixed(2)}',
                      color: Colors.red[100]!,
                      icon: Icons.arrow_downward,
                      iconColor: Colors.red,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: _buildPriceStatCard(
                      localizations.avgPrice,
                      '₹${price.avgMonthPrice.toStringAsFixed(2)}',
                      color: Colors.blue[100]!,
                      icon: Icons.dns,
                      iconColor: Colors.blue,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: _buildPriceStatCard(
                      localizations.maxPrice,
                      '₹${price.maxMonthPrice.toStringAsFixed(2)}',
                      color: Colors.green[100]!,
                      icon: Icons.arrow_upward,
                      iconColor: Colors.green,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24),
              Text(
                localizations.priceHistory,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Expanded(
                child: FutureBuilder<List<Map<String, dynamic>>>(
                  future: MarketPriceService.getPriceHistory(price.productId),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red[300],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Failed to load price history',
                              style: TextStyle(color: Colors.grey[700]),
                            ),
                            SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: () {
                                setState(() {}); // Refresh the FutureBuilder
                              },
                              child: Text(localizations.tryAgain),
                            ),
                          ],
                        ),
                      );
                    }

                    final priceHistory = snapshot.data ?? [];

                    if (priceHistory.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.bar_chart,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No price history available',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      );
                    }

                    return PriceHistoryChart(
                      priceHistory: priceHistory,
                      lineColor: Colors.red,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPriceStatCard(
    String label,
    String value, {
    required Color color,
    required IconData icon,
    required Color iconColor,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: iconColor),
          SizedBox(height: 8),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[800])),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
