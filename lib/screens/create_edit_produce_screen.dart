// lib/screens/create_edit_produce_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/services/farmer_produce_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:intl/intl.dart';
import 'dart:typed_data';

class CreateEditProduceScreen extends StatefulWidget {
  final FarmerProduce?
  produce; // If provided, we're editing; otherwise creating

  const CreateEditProduceScreen({Key? key, this.produce}) : super(key: key);

  @override
  State<CreateEditProduceScreen> createState() =>
      _CreateEditProduceScreenState();
}

class _CreateEditProduceScreenState extends State<CreateEditProduceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _varietyController = TextEditingController();
  final _quantityController = TextEditingController();
  final _descriptionController = TextEditingController();

  int? _selectedCropId;
  String _selectedUnitType = 'kg'; // Default unit type
  DateTime _availableFrom = DateTime.now();
  DateTime? _availableUntil;
  DateTime? _harvestDate;

  File? _primaryImage;
  File? _secondaryImage;
  File? _tertiaryImage;

  // For existing images in edit mode
  String? _existingPrimaryImage;
  String? _existingSecondaryImage;
  String? _existingTertiaryImage;

  bool _isLoading = false;
  bool _isCropsLoading = true;
  String? _errorMessage;
  bool _isEdit = false;

  // Available crops from API
  List<Map<String, dynamic>> _crops = [];

  // Available unit types
  final List<String> _unitTypes = ['kg', 'g', 'ton', 'piece', 'bundle', 'box'];

  @override
  void initState() {
    super.initState();
    _isEdit = widget.produce != null;

    _loadCrops();

    if (_isEdit) {
      _initEditMode();
    }
  }

  Future<void> _loadCrops() async {
    try {
      setState(() {
        _isCropsLoading = true;
      });

      final crops = await FarmerProduceService.getAvailableCrops();

      setState(() {
        _crops = crops;
        _isCropsLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = "Error loading crops: ${e.toString()}";
        _isCropsLoading = false;
        // Use fallback data
        _crops = [
          {'id': 1, 'crop_name': 'Rice'},
          {'id': 2, 'crop_name': 'Wheat'},
          {'id': 3, 'crop_name': 'Corn'},
          {'id': 4, 'crop_name': 'Tomato'},
          {'id': 5, 'crop_name': 'Potato'},
        ];
      });
    }
  }

  void _initEditMode() {
    // Set values from existing produce
    final produce = widget.produce!;

    _selectedCropId = produce.productId;
    _varietyController.text = produce.variety ?? '';
    _quantityController.text = produce.quantity.toString();
    _selectedUnitType = produce.unitType;
    _descriptionController.text = produce.description ?? '';

    if (produce.availableFrom.isNotEmpty) {
      _availableFrom = DateTime.parse(produce.availableFrom);
    }

    if (produce.availableUntil != null && produce.availableUntil!.isNotEmpty) {
      _availableUntil = DateTime.parse(produce.availableUntil!);
    }

    if (produce.harvestDate != null && produce.harvestDate!.isNotEmpty) {
      _harvestDate = DateTime.parse(produce.harvestDate!);
    }

    // Store existing image URLs
    _existingPrimaryImage = produce.primaryImageUrl;
    _existingSecondaryImage = produce.secondaryImageUrl;
    _existingTertiaryImage = produce.tertiaryImageUrl;
  }

  @override
  void dispose() {
    _varietyController.dispose();
    _quantityController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectImage(int imageIndex) async {
    final localizations = AppLocalizations.of(context)!;
    final ImagePicker picker = ImagePicker();
    final ImageSource? source = await showModalBottomSheet<ImageSource>(
      context: context,
      builder:
          (BuildContext context) => SafeArea(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: const Icon(Icons.photo_camera),
                  title: Text(localizations.takePhoto),
                  onTap: () => Navigator.pop(context, ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: Text(localizations.chooseFromGallery),
                  onTap: () => Navigator.pop(context, ImageSource.gallery),
                ),
              ],
            ),
          ),
    );

    if (source == null) return;

    try {
      final XFile? pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 80, // Reduce quality to ensure compatibility
      );

      if (pickedFile != null) {
        // For JPG/PNG files, just use them directly
        // This avoids the HEIC/HEIF conversion issues
        setState(() {
          switch (imageIndex) {
            case 1:
              _primaryImage = File(pickedFile.path);
              break;
            case 2:
              _secondaryImage = File(pickedFile.path);
              break;
            case 3:
              _tertiaryImage = File(pickedFile.path);
              break;
          }
        });
      }
    } catch (e) {
      print("Error processing image: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.errorProcessingImage)),
      );
    }
  }

  Future<void> _selectDate(BuildContext context, int dateType) async {
    DateTime? initialDate;
    DateTime firstDate = DateTime.now().subtract(const Duration(days: 365));

    switch (dateType) {
      case 1: // Available From
        initialDate = _availableFrom;
        break;
      case 2: // Available Until
        initialDate =
            _availableUntil ?? _availableFrom.add(const Duration(days: 30));
        firstDate = _availableFrom;
        break;
      case 3: // Harvest Date
        initialDate =
            _harvestDate ?? DateTime.now().subtract(const Duration(days: 30));
        break;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        switch (dateType) {
          case 1:
            _availableFrom = picked;
            // If available until is before the new available from, reset it
            if (_availableUntil != null &&
                _availableUntil!.isBefore(_availableFrom)) {
              _availableUntil = null;
            }
            break;
          case 2:
            _availableUntil = picked;
            break;
          case 3:
            _harvestDate = picked;
            break;
        }
      });
    }
  }

  Future<void> _saveProduce() async {
    final localizations = AppLocalizations.of(context)!;

    if (!_formKey.currentState!.validate()) return;

    if (_selectedCropId == null) {
      setState(() {
        _errorMessage = localizations.selectCrop;
      });
      return;
    }

    if (_primaryImage == null && _existingPrimaryImage == null) {
      setState(() {
        _errorMessage = localizations.addPrimaryImage;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Prepare data
      Map<String, dynamic> produceData = {
        'product_id': _selectedCropId,
        'variety': _varietyController.text.trim(),
        'quantity': double.parse(_quantityController.text),
        'unit_type': _selectedUnitType,
        'description': _descriptionController.text.trim(),
        'available_from': DateFormat('yyyy-MM-dd').format(_availableFrom),
      };

      if (_availableUntil != null) {
        produceData['available_until'] = DateFormat(
          'yyyy-MM-dd',
        ).format(_availableUntil!);
      }

      if (_harvestDate != null) {
        produceData['harvest_date'] = DateFormat(
          'yyyy-MM-dd',
        ).format(_harvestDate!);
      }

      bool success;
      if (_isEdit) {
        // Update existing produce
        success = await FarmerProduceService.updateProduce(
          widget.produce!.id,
          produceData,
          primaryImage: _primaryImage,
          secondaryImage: _secondaryImage,
          tertiaryImage: _tertiaryImage,
        );
      } else {
        // Create new produce
        final produceId = await FarmerProduceService.createProduce(
          produceData,
          primaryImage: _primaryImage,
          secondaryImage: _secondaryImage,
          tertiaryImage: _tertiaryImage,
        );
        success = produceId > 0;
      }

      if (success) {
        Navigator.pop(context, true); // Return true to indicate success
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage =
              _isEdit
                  ? localizations.failedToUpdateProduce
                  : localizations.failedToCreateProduce;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEdit ? localizations.editProduce : localizations.addNewProduce,
        ),
      ),
      body:
          _isCropsLoading
              ? Center(child: CircularProgressIndicator())
              : GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.all(16.0),
                    children: [
                      if (_errorMessage != null)
                        Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error_outline, color: Colors.red[700]),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(color: Colors.red[700]),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Crop selection
                      Text(
                        localizations.crops,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<int>(
                        value: _selectedCropId,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        hint: Text(localizations.selectCrop),
                        items:
                            _crops.map((crop) {
                              return DropdownMenuItem<int>(
                                value: crop['id'],
                                child: Text(crop['crop_name']),
                              );
                            }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCropId = value;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return localizations.selectCrop;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Variety field
                      Text(
                        localizations.varietyOptional,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _varietyController,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          hintText: localizations.varietyHint,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Quantity and unit type
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Quantity field
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  localizations.quantity,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextFormField(
                                  controller: _quantityController,
                                  keyboardType: TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.grey[100],
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    contentPadding: const EdgeInsets.all(16),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return localizations.quantityRequired;
                                    }
                                    if (double.tryParse(value) == null) {
                                      return localizations.enterValidNumber;
                                    }
                                    if (double.parse(value) <= 0) {
                                      return localizations
                                          .quantityGreaterThanZero;
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Unit type dropdown
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  localizations.unit,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                DropdownButtonFormField<String>(
                                  value: _selectedUnitType,
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.grey[100],
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                  ),
                                  items:
                                      _unitTypes.map((unit) {
                                        return DropdownMenuItem<String>(
                                          value: unit,
                                          child: Text(unit),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedUnitType = value!;
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Description field
                      Text(
                        localizations.descriptionOptional,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          hintText: localizations.describeProduceHint,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Date fields
                      Text(
                        localizations.availability,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Available From date
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(localizations.availableFrom("")),
                        subtitle: Text(
                          DateFormat.yMMMd().format(_availableFrom),
                        ),
                        trailing: Icon(Icons.calendar_today),
                        onTap: () => _selectDate(context, 1),
                      ),

                      // Available Until date (optional)
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(localizations.availableUntilOptional),
                        subtitle:
                            _availableUntil != null
                                ? Text(
                                  DateFormat.yMMMd().format(_availableUntil!),
                                )
                                : Text(localizations.notSpecified),
                        trailing: Icon(Icons.calendar_today),
                        onTap: () => _selectDate(context, 2),
                      ),

                      // Harvest Date (optional)
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(localizations.harvestDateOptional),
                        subtitle:
                            _harvestDate != null
                                ? Text(DateFormat.yMMMd().format(_harvestDate!))
                                : Text(localizations.notSpecified),
                        trailing: Icon(Icons.calendar_today),
                        onTap: () => _selectDate(context, 3),
                      ),

                      Divider(),
                      const SizedBox(height: 16),

                      // Image upload section
                      Text(
                        localizations.images,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Primary Image (required)
                      Text(
                        localizations.primaryImageRequired,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      _buildImagePicker(
                        1,
                        _primaryImage,
                        _existingPrimaryImage,
                        true,
                      ),
                      const SizedBox(height: 16),

                      // Secondary Image (optional)
                      Text(
                        localizations.secondaryImageOptional,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      _buildImagePicker(
                        2,
                        _secondaryImage,
                        _existingSecondaryImage,
                        false,
                      ),
                      const SizedBox(height: 16),

                      // Tertiary Image (optional)
                      Text(
                        localizations.tertiaryImageOptional,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      _buildImagePicker(
                        3,
                        _tertiaryImage,
                        _existingTertiaryImage,
                        false,
                      ),

                      const SizedBox(height: 24),

                      // Submit button
                      ElevatedButton(
                        onPressed: _isLoading ? null : _saveProduce,
                        child:
                            _isLoading
                                ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                                : Text(
                                  _isEdit
                                      ? localizations.updateProduce
                                      : localizations.addProduce,
                                ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildImagePicker(
    int imageIndex,
    File? selectedImage,
    String? existingImage,
    bool isRequired,
  ) {
    final localizations = AppLocalizations.of(context)!;

    // If new image is selected, show that
    if (selectedImage != null) {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              selectedImage,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Material(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  setState(() {
                    switch (imageIndex) {
                      case 1:
                        _primaryImage = null;
                        break;
                      case 2:
                        _secondaryImage = null;
                        break;
                      case 3:
                        _tertiaryImage = null;
                        break;
                    }
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(Icons.close, size: 20, color: Colors.red[700]),
                ),
              ),
            ),
          ),
        ],
      );
    }

    // If there's an existing image (in edit mode), show that
    if (existingImage != null && existingImage.isNotEmpty) {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              existingImage,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  height: 150,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: Center(child: CircularProgressIndicator()),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: Center(
                    child: Icon(
                      Icons.error_outline,
                      color: Colors.red[400],
                      size: 40,
                    ),
                  ),
                );
              },
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Material(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  setState(() {
                    switch (imageIndex) {
                      case 1:
                        _existingPrimaryImage = null;
                        break;
                      case 2:
                        _existingSecondaryImage = null;
                        break;
                      case 3:
                        _existingTertiaryImage = null;
                        break;
                    }
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(Icons.close, size: 20, color: Colors.red[700]),
                ),
              ),
            ),
          ),
        ],
      );
    }

    // Otherwise show the "add image" container
    return InkWell(
      onTap: () => _selectImage(imageIndex),
      child: Container(
        height: 150,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isRequired && imageIndex == 1 ? Colors.red : Colors.grey[300]!,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.add_a_photo, size: 48, color: Colors.grey[600]),
              const SizedBox(height: 8),
              Text(
                isRequired
                    ? localizations.requiredTapToAddImage
                    : localizations.tapToAddImage,
                style: TextStyle(color: Colors.grey[600]),
              ),
              if (isRequired)
                Text(
                  localizations.primaryImageRequiredError,
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
