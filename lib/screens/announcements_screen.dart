// lib/screens/announcements_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/announcement.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/screens/announcement_detail_screen.dart';
import 'package:fpo_traders/services/announcement_service.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:intl/intl.dart';

class AnnouncementsScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const AnnouncementsScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<AnnouncementsScreen> createState() => _AnnouncementsScreenState();
}

class _AnnouncementsScreenState extends State<AnnouncementsScreen> {
  final ScrollController _scrollController = ScrollController();

  List<Announcement> _announcements = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _fetchAnnouncements();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          !_isLoadingMore &&
          _hasMorePages) {
        _loadMoreAnnouncements(AppLocalizations.of(context)!);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchAnnouncements({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _announcements = [];
      });
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final result = await AnnouncementService.getAnnouncements(
        page: _currentPage,
        limit: 10,
      );

      final announcements = result['announcements'] as List<Announcement>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        if (refresh) {
          _announcements = announcements;
        } else {
          _announcements.addAll(announcements);
        }

        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreAnnouncements(AppLocalizations localizations) async {
    if (!_hasMorePages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final result = await AnnouncementService.getAnnouncements(
        page: _currentPage,
        limit: 10,
      );

      final announcements = result['announcements'] as List<Announcement>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _announcements.addAll(announcements);
        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.failedToLoadMoreAnnouncements)),
        );
      });
    }
  }

  Future<void> _refreshAnnouncements() {
    return _fetchAnnouncements(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (widget.isContentOnly) {
      return _buildBody(localizations);
    }

    return Scaffold(
      appBar: AppBar(title: Text(localizations.announcements)),
      body: _buildBody(localizations),
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 0, // Home section
        user: widget.user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading && _announcements.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError && _announcements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              localizations.failedToLoadAnnouncements,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshAnnouncements,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_announcements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.campaign_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              localizations.noAnnouncementsAvailable,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              localizations.checkBackLaterForUpdates,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshAnnouncements,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16),
        itemCount: _announcements.length + (_hasMorePages ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _announcements.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final announcement = _announcements[index];
          return _buildAnnouncementCard(announcement);
        },
      ),
    );
  }

  Widget _buildAnnouncementCard(Announcement announcement) {
    final DateTime createdAt = DateTime.parse(announcement.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      AnnouncementDetailScreen(announcementId: announcement.id),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Announcement image
            if (announcement.imageUrl != null)
              Container(
                height: 160,
                width: double.infinity,
                color: Colors.grey[300],
                child: Image.network(
                  announcement.imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[500],
                        size: 48,
                      ),
                    );
                  },
                ),
              ),

            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    announcement.title,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8),

                  // Description
                  Text(
                    announcement.description,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey[700], fontSize: 14),
                  ),
                  SizedBox(height: 16),

                  // Metadata row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 4),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(Icons.person, size: 14, color: Colors.grey[600]),
                          SizedBox(width: 4),
                          Text(
                            announcement.createdByName,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
