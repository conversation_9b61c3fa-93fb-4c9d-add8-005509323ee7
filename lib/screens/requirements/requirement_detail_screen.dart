// lib/screens/requirements/requirement_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/models/requirement.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/services/requirement_service.dart';
import 'package:fpo_traders/services/farmer_produce_service.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/screens/requirements/express_interest_screen.dart';
import 'package:fpo_traders/screens/requirements/create_produce_screen.dart';

class RequirementDetailScreen extends StatefulWidget {
  final int requirementId;
  final User user;

  const RequirementDetailScreen({
    Key? key,
    required this.requirementId,
    required this.user,
  }) : super(key: key);

  @override
  State<RequirementDetailScreen> createState() =>
      _RequirementDetailScreenState();
}

class _RequirementDetailScreenState extends State<RequirementDetailScreen> {
  bool _isLoading = true;
  bool _isLoadingProduce = false;
  Requirement? _requirement;
  List<FarmerProduce> _matchingProduce = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadRequirementDetails();
  }

  Future<void> _loadRequirementDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final requirement = await RequirementService.getRequirementDetails(
        widget.requirementId,
      );

      setState(() {
        _requirement = requirement;
        _isLoading = false;
      });

      // After loading requirement details, load matching produce
      _loadMatchingProduce();
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error loading requirement details: $_errorMessage');
    }
  }

  Future<void> _loadMatchingProduce() async {
    if (_requirement == null) return;

    setState(() {
      _isLoadingProduce = true;
    });

    try {
      // Get the farmer's available produce that matches this crop
      final result = await FarmerProduceService.getMyProduce(
        status: 'available',
      );

      final allProduce = result['produce'] as List<FarmerProduce>;

      // Filter only for matching product_id
      final matchingProduce =
          allProduce
              .where((produce) => produce.productId == _requirement!.productId)
              .toList();

      setState(() {
        _matchingProduce = matchingProduce;
        _isLoadingProduce = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProduce = false;
      });
      print('Error loading matching produce: $e');
    }
  }

  void _navigateToExpressInterest(FarmerProduce produce) {
    if (_requirement == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ExpressInterestScreen(
              requirement: _requirement!,
              produce: produce,
              user: widget.user,
            ),
      ),
    ).then((_) {
      // Refresh data when returning from express interest screen
      _loadRequirementDetails();
    });
  }

  void _navigateToCreateProduce() {
    if (_requirement == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => CreateProduceScreen(
              requirement: _requirement!,
              user: widget.user,
            ),
      ),
    ).then((_) {
      // Refresh data when returning from create produce screen
      _loadRequirementDetails();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text('Requirement Details')),
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? _buildErrorWidget()
              : _requirement != null
              ? _buildRequirementDetails(localizations)
              : Center(child: Text('Requirement not found')),
      bottomNavigationBar:
          _requirement != null && _requirement!.status == 'active'
              ? _buildBottomActions()
              : null,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
          SizedBox(height: 16),
          Text(
            'Failed to load requirement details',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              _errorMessage ?? 'An error occurred. Please try again.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadRequirementDetails,
            child: Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementDetails(AppLocalizations localizations) {
    final requirement = _requirement!;

    // Format dates
    final formattedCreatedAt = DateFormat.yMMMd().format(
      DateTime.parse(requirement.createdAt),
    );

    String? formattedRequiredBy;
    if (requirement.requiredBy != null) {
      formattedRequiredBy = DateFormat.yMMMd().format(
        DateTime.parse(requirement.requiredBy!),
      );
    }

    // Format prices
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    final priceOffered = priceFormat.format(requirement.priceOffered);

    String? marketPrice;
    String? priceDifference;
    if (requirement.marketPriceAtCreation != null) {
      marketPrice = priceFormat.format(requirement.marketPriceAtCreation!);

      final difference = requirement.getPriceDifference();
      if (difference != null) {
        final sign = difference > 0 ? '+' : '';
        priceDifference = '$sign${priceFormat.format(difference)}';
      }
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with crop image
          Stack(
            children: [
              // Crop image or placeholder
              Container(
                height: 200,
                width: double.infinity,
                color: Colors.grey[300],
                child:
                    requirement.cropImageUrl != null
                        ? Image.network(
                          requirement.cropImageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.image,
                                size: 64,
                                color: Colors.grey[500],
                              ),
                            );
                          },
                        )
                        : Center(
                          child: Icon(
                            Icons.agriculture,
                            size: 64,
                            color: Colors.grey[500],
                          ),
                        ),
              ),

              // Gradient overlay for better text visibility
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ),

              // Status badge
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(requirement.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    requirement.status.toUpperCase(),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),

              // Crop name and variety
              Positioned(
                bottom: 16,
                left: 16,
                right: 70,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      requirement.cropName,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (requirement.variety != null &&
                        requirement.variety!.isNotEmpty)
                      Text(
                        requirement.variety!,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          // Basic information section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Requirement Details'),

                _buildInfoRow(
                  Icons.inventory_2,
                  'Quantity Needed',
                  '${requirement.quantity} ${requirement.unitType}',
                ),
                SizedBox(height: 12),

                _buildInfoRow(
                  Icons.monetization_on,
                  'Price Offered',
                  '$priceOffered per ${requirement.unitType}',
                  valueColor: Colors.green[700],
                ),
                SizedBox(height: 12),

                if (requirement.requiredBy != null)
                  _buildInfoRow(
                    Icons.event,
                    'Required By',
                    formattedRequiredBy!,
                  ),
                if (requirement.requiredBy != null) SizedBox(height: 12),

                if (marketPrice != null)
                  _buildInfoRow(
                    Icons.show_chart,
                    'Market Price',
                    '$marketPrice per ${requirement.marketPriceUnit ?? requirement.unitType}',
                    valueColor: Colors.blue[700],
                  ),
                if (marketPrice != null) SizedBox(height: 4),

                if (priceDifference != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 36.0),
                    child: Row(
                      children: [
                        Text(
                          'Price difference: ',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          priceDifference,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color:
                                requirement.isPriceFavorable()
                                    ? Colors.green[700]
                                    : Colors.red[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                SizedBox(height: 24),

                // Description section
                if (requirement.description != null &&
                    requirement.description!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionHeader('Description'),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          requirement.description!,
                          style: TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                      SizedBox(height: 24),
                    ],
                  ),

                // Creation info
                _buildSectionHeader('Additional Information'),
                _buildInfoRow(
                  Icons.person,
                  'Posted By',
                  requirement.createdByName,
                ),
                SizedBox(height: 12),
                _buildInfoRow(
                  Icons.calendar_today,
                  'Posted On',
                  formattedCreatedAt,
                ),
                SizedBox(height: 12),
                _buildInfoRow(
                  Icons.people,
                  'Interested Farmers',
                  '${requirement.interestCount}',
                ),
                SizedBox(height: 24),

                // Matching produce section
                _buildSectionHeader('Your Matching Produce'),
                SizedBox(height: 8),

                _isLoadingProduce
                    ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                    : _matchingProduce.isEmpty
                    ? _buildNoMatchingProduceMessage()
                    : _buildMatchingProduceList(),

                // Space at the bottom for the fixed action buttons
                SizedBox(height: 100),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: valueColor ?? Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNoMatchingProduceMessage() {
    // Only show the message if they haven't expressed interest yet
    if (_requirement!.hasExpressedInterest) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[300]!),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[700]),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'You have already expressed interest in this requirement',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (_requirement!.interestDetails != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Details: ${_requirement!.interestDetails!['notes'] ?? 'No additional details'}',
                  style: TextStyle(color: Colors.green[700]),
                ),
              ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Icon(Icons.agriculture_outlined, size: 36, color: Colors.grey[400]),
          SizedBox(height: 8),
          Text(
            'You don\'t have any available produce that matches this requirement.',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey[700]),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchingProduceList() {
    if (_requirement!.hasExpressedInterest) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[300]!),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[700]),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'You have already expressed interest in this requirement',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (_requirement!.interestDetails != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Details: ${_requirement!.interestDetails!['notes'] ?? 'No additional details'}',
                  style: TextStyle(color: Colors.green[700]),
                ),
              ),
          ],
        ),
      );
    }

    return Column(
      children: [
        for (var produce in _matchingProduce)
          Card(
            margin: EdgeInsets.only(bottom: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: InkWell(
              onTap: () => _navigateToExpressInterest(produce),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    // Produce image
                    if (produce.primaryImageUrl != null)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          produce.primaryImageUrl!,
                          width: 70,
                          height: 70,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 70,
                              height: 70,
                              color: Colors.grey[300],
                              child: Icon(Icons.image, color: Colors.grey[500]),
                            );
                          },
                        ),
                      )
                    else
                      Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.image, color: Colors.grey[500]),
                      ),
                    SizedBox(width: 12),

                    // Produce details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (produce.variety != null &&
                              produce.variety!.isNotEmpty)
                            Text(
                              produce.variety!,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          SizedBox(height: 4),
                          Text(
                            'Quantity: ${produce.quantity} ${produce.unitType}',
                            style: TextStyle(fontSize: 14),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Available from: ${DateFormat.yMMMd().format(DateTime.parse(produce.availableFrom))}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Express interest button
                    IconButton(
                      icon: Icon(Icons.arrow_forward_ios, size: 16),
                      onPressed: () => _navigateToExpressInterest(produce),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBottomActions() {
    // If user has already expressed interest, show a disabled button
    if (_requirement!.hasExpressedInterest) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[400],
            padding: EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            'Interest Expressed',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      );
    }

    // If there's matching produce, show Express Interest button
    if (_matchingProduce.isNotEmpty) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () => _navigateToExpressInterest(_matchingProduce.first),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[900],
            padding: EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            'Express Interest',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      );
    }

    // If no matching produce, show Create Produce button
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _navigateToCreateProduce,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green[700],
          padding: EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          'Create Produce',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'fulfilled':
        return Colors.blue;
      case 'expired':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
