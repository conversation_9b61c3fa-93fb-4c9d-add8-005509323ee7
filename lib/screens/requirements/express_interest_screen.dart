// lib/screens/requirements/express_interest_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/models/requirement.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/services/requirement_service.dart';
import 'package:intl/intl.dart';

class ExpressInterestScreen extends StatefulWidget {
  final Requirement requirement;
  final FarmerProduce produce;
  final User user;

  const ExpressInterestScreen({
    Key? key,
    required this.requirement,
    required this.produce,
    required this.user,
  }) : super(key: key);

  @override
  State<ExpressInterestScreen> createState() => _ExpressInterestScreenState();
}

class _ExpressInterestScreenState extends State<ExpressInterestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Initialize with the available quantity or the required quantity, whichever is smaller
    final initialQuantity =
        widget.produce.quantity < widget.requirement.quantity
            ? widget.produce.quantity
            : widget.requirement.quantity;
    _quantityController.text = initialQuantity.toString();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _submitInterest() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await RequirementService.expressInterest(
        requirementId: widget.requirement.id,
        produceId: widget.produce.id,
        quantity: double.parse(_quantityController.text),
        notes: _notesController.text.isEmpty ? null : _notesController.text,
      );

      // Show success message and navigate back
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Interest expressed successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Close the screen and return to the requirement details
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error expressing interest: $_errorMessage');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    // Format the price for display
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    final pricePerUnit = priceFormat.format(widget.requirement.priceOffered);

    return Scaffold(
      appBar: AppBar(title: Text('Express Interest')),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Summary card with requirement and produce details
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Interest Summary',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Divider(),
                        SizedBox(height: 8),

                        // Requirement details
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.assignment, color: Colors.green[700]),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Requirement',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    '${widget.requirement.cropName}${widget.requirement.variety != null ? " (${widget.requirement.variety})" : ""}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    'Needed: ${widget.requirement.quantity} ${widget.requirement.unitType}',
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    'Price: $pricePerUnit per ${widget.requirement.unitType}',
                                    style: TextStyle(
                                      color: Colors.green[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        SizedBox(height: 16),

                        // Produce details
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.agriculture, color: Colors.blue[700]),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Your Produce',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  if (widget.produce.variety != null &&
                                      widget.produce.variety!.isNotEmpty)
                                    Text(
                                      widget.produce.variety!,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  SizedBox(height: 4),
                                  Text(
                                    'Available: ${widget.produce.quantity} ${widget.produce.unitType}',
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    'Available from: ${DateFormat.yMMMd().format(DateTime.parse(widget.produce.availableFrom))}',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 24),

                // Quantity input
                Text(
                  'Quantity',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                TextFormField(
                  controller: _quantityController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: 'Enter quantity',
                    border: OutlineInputBorder(),
                    suffixText: widget.produce.unitType,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Quantity is required';
                    }

                    final quantity = double.tryParse(value);
                    if (quantity == null) {
                      return 'Please enter a valid number';
                    }

                    if (quantity <= 0) {
                      return 'Quantity must be greater than 0';
                    }

                    if (quantity > widget.produce.quantity) {
                      return 'Quantity cannot exceed available produce (${widget.produce.quantity} ${widget.produce.unitType})';
                    }

                    return null;
                  },
                ),

                SizedBox(height: 24),

                // Notes input
                Text(
                  'Additional Notes (Optional)',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                TextFormField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Add any notes or details about your produce...',
                    border: OutlineInputBorder(),
                  ),
                ),

                // Error message if present
                if (_errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 24.0),
                    child: Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[700]),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                SizedBox(height: 32),

                // Transaction calculation
                Card(
                  elevation: 1,
                  color: Colors.grey[50],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(color: Colors.grey[200]!),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Transaction Summary',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 12),
                        _buildTransactionRow(
                          'Price per ${widget.produce.unitType}',
                          pricePerUnit,
                        ),
                        SizedBox(height: 8),
                        _buildTransactionRow(
                          'Quantity',
                          _quantityController.text +
                              ' ' +
                              widget.produce.unitType,
                        ),
                        Divider(height: 24),
                        _buildTransactionRow(
                          'Total Amount',
                          _calculateTotal(),
                          isBold: true,
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 32),

                // Submit button
                Container(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submitInterest,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Colors.green[700],
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        _isLoading
                            ? CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            )
                            : Text(
                              'Submit Interest',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),

                SizedBox(height: 36),

                // Note about the process
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'What happens next?',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[800],
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'After expressing interest, your produce will be marked as "pending" and the FPO will be notified. They will review your offer and contact you for next steps.',
                        style: TextStyle(color: Colors.blue[800]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionRow(
    String label,
    String value, {
    bool isBold = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[700],
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: isBold ? Colors.green[700] : Colors.black87,
          ),
        ),
      ],
    );
  }

  String _calculateTotal() {
    final quantity = double.tryParse(_quantityController.text) ?? 0.0;
    final pricePerUnit = widget.requirement.priceOffered;
    final total = quantity * pricePerUnit;

    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    return priceFormat.format(total);
  }
}
