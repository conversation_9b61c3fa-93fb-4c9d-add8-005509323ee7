// lib/screens/requirements/requirements_list_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/models/requirement.dart';
import 'package:fpo_traders/services/requirement_service.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/screens/requirements/requirement_detail_screen.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/models/requirement.dart';

class RequirementsListScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const RequirementsListScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<RequirementsListScreen> createState() => _RequirementsListScreenState();
}

class _RequirementsListScreenState extends State<RequirementsListScreen>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  List<dynamic> _requirements = [];
  String? _errorMessage;
  late TabController _tabController;
  int _currentPage = 1;
  bool _hasMorePages = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);
    _fetchRequirements();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          _hasMorePages) {
        _loadMoreRequirements();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging ||
        _tabController.index != _tabController.previousIndex) {
      setState(() {
        _requirements = [];
        _currentPage = 1;
      });
      _fetchRequirements();
    }
  }

  Future<void> _fetchRequirements() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await RequirementService.getRequirements(
        page: _currentPage,
        limit: 10,
        type: _getCurrentRequirementsType(),
      );

      setState(() {
        // Convert raw JSON data to Requirement objects
        _requirements =
            (result['data'] as List)
                .map((json) => Requirement.fromJson(json))
                .toList();
        final currentPage = result['pagination']?['currentPage'] ?? 1;
        final totalPages = result['pagination']?['totalPages'] ?? 1;

        _hasMorePages = currentPage < totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error fetching requirements: $_errorMessage');
    }
  }

  Future<void> _loadMoreRequirements() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _currentPage++;
    });

    try {
      final result = await RequirementService.getRequirements(
        page: _currentPage,
        limit: 10,
        type: _getCurrentRequirementsType(),
      );

      setState(() {
        // Convert raw JSON data to Requirement objects
        _requirements.addAll(
          (result['data'] as List)
              .map((json) => Requirement.fromJson(json))
              .toList(),
        );
        final currentPage = result['pagination']?['currentPage'] ?? 1;
        final totalPages = result['pagination']?['totalPages'] ?? 1;
        _hasMorePages = currentPage < totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentPage--;
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load more requirements')),
      );
    }
  }

  String _getCurrentRequirementsType() {
    switch (_tabController.index) {
      case 0:
        return 'active';
      case 1:
        return 'for-my-crops';
      case 2:
        return 'matching';
      default:
        return 'active';
    }
  }

  Future<void> _refreshRequirements() {
    setState(() {
      _requirements = [];
      _currentPage = 1;
    });
    return _fetchRequirements();
  }

  @override
  Widget build(BuildContext context) {
    // In content-only mode, just return the content
    if (widget.isContentOnly) {
      return _buildContentWithTabs();
    }

    // Otherwise return the full scaffold with bottom navigation
    return Scaffold(
      appBar: AppBar(
        title: Text('FPO Requirements'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'All Active'),
            Tab(text: 'For My Crops'),
            Tab(text: 'Matching'),
          ],
        ),
      ),
      body: _buildTabContent(),
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 2, // Match your navigation index for requirements
        user: widget.user,
      ),
    );
  }

  // Content with tabs for content-only mode
  Widget _buildContentWithTabs() {
    return Column(
      children: [
        // Tabs for content-only mode
        Container(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          child: TabBar(
            controller: _tabController,
            tabs: [
              Tab(text: 'All Active'),
              Tab(text: 'For My Crops'),
              Tab(text: 'Matching'),
            ],
            labelColor: Theme.of(context).primaryColor,
            indicatorColor: Theme.of(context).primaryColor,
          ),
        ),

        // Main content - expanded to fill available space
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshRequirements,
            child: _buildTabContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabContent() {
    if (_isLoading && _requirements.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null && _requirements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              'Failed to load requirements',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshRequirements,
              child: Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_requirements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No requirements found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              _getEmptyStateMessage(),
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16),
      itemCount: _requirements.length + (_hasMorePages ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _requirements.length) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final requirement = _requirements[index];
        return _buildRequirementCard(requirement);
      },
    );
  }

  String _getEmptyStateMessage() {
    switch (_tabController.index) {
      case 0:
        return 'There are no active requirements at the moment.';
      case 1:
        return 'No requirements found for your registered crops.\nAdd more crops to see relevant requirements.';
      case 2:
        return 'You don\'t have any available produce that matches current requirements.';
      default:
        return 'No requirements found.';
    }
  }

  Widget _buildRequirementCard(Requirement requirement) {
    final requiredByDate =
        requirement.requiredBy != null
            ? DateFormat.yMMMd().format(DateTime.parse(requirement.requiredBy!))
            : 'No deadline';

    final price = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    ).format(requirement.priceOffered);

    // Check if the user has already expressed interest
    final bool hasExpressedInterest = requirement.hasExpressedInterest;

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => RequirementDetailScreen(
                    requirementId: requirement.id,
                    user: widget.user,
                  ),
            ),
          ).then((_) {
            // Refresh the list when returning from detail screen
            _refreshRequirements();
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with crop image
            Container(
              height: 120,
              decoration: BoxDecoration(color: Colors.grey[200]),
              child: Stack(
                children: [
                  // Crop image
                  if (requirement.cropImageUrl != null &&
                      requirement.cropImageUrl!.isNotEmpty)
                    Positioned.fill(
                      child: Image.network(
                        requirement.cropImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Icon(
                              Icons.image,
                              size: 48,
                              color: Colors.grey[500],
                            ),
                          );
                        },
                      ),
                    ),

                  // Gradient overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.1),
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Crop name
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Text(
                      requirement.cropName,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // If there's a variety, display it
                  if (requirement.variety != null &&
                      requirement.variety!.isNotEmpty)
                    Positioned(
                      bottom: 16,
                      right: 16,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          requirement.variety!,
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Requirement details
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quantity and unit
                  Row(
                    children: [
                      Icon(
                        Icons.inventory_2,
                        size: 16,
                        color: Colors.grey[700],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${requirement.quantity} ${requirement.unitType}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),

                  // Price offered
                  Row(
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 16,
                        color: Colors.green[700],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '$price per ${requirement.unitType}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),

                  // Required by date
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey[700],
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Required by: $requiredByDate',
                        style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                      ),
                    ],
                  ),

                  SizedBox(height: 16),

                  // Available quantity for matching tab
                  if (_tabController.index == 2 &&
                      requirement.availableQuantity != null)
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.blue[700],
                          ),
                          SizedBox(width: 4),
                          Text(
                            'You have ${requirement.availableQuantity} ${requirement.unitType} available',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Show "Interest Expressed" badge if user has already expressed interest
                  if (hasExpressedInterest)
                    Container(
                      margin: EdgeInsets.only(top: 16),
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[300]!),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.green[700],
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Interest Expressed',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                  SizedBox(height: 12),

                  // Interest count if available
                  if (requirement.interestCount > 0)
                    Row(
                      children: [
                        Icon(Icons.people, size: 14, color: Colors.grey[600]),
                        SizedBox(width: 4),
                        Text(
                          '${requirement.interestCount} farmers interested',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),

                  SizedBox(height: 16),

                  // Action button at the bottom - disabled if interest already expressed
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          hasExpressedInterest
                              ? null
                              : () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => RequirementDetailScreen(
                                          requirementId: requirement.id,
                                          user: widget.user,
                                        ),
                                  ),
                                ).then((_) {
                                  _refreshRequirements();
                                });
                              },
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12),
                        // Don't set backgroundColor directly - let the disabled state handle it
                        disabledBackgroundColor: Colors.grey[400],
                        backgroundColor: Colors.green[700],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        hasExpressedInterest
                            ? 'Interest Expressed'
                            : 'Express Interest',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
