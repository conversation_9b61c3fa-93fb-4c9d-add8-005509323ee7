// lib/screens/requirements/create_produce_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/models/requirement.dart';
import 'package:fpo_traders/services/requirement_service.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;

class CreateProduceScreen extends StatefulWidget {
  final Requirement requirement;
  final User user;

  const CreateProduceScreen({
    Key? key,
    required this.requirement,
    required this.user,
  }) : super(key: key);

  @override
  State<CreateProduceScreen> createState() => _CreateProduceScreenState();
}

class _CreateProduceScreenState extends State<CreateProduceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _varietyController = TextEditingController();
  final _quantityController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime _availableFrom = DateTime.now();
  DateTime? _availableUntil;
  DateTime? _harvestDate;

  File? _primaryImage;
  File? _secondaryImage;
  File? _tertiaryImage;

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Initialize with the requirement quantity
    _quantityController.text = widget.requirement.quantity.toString();

    // If requirement has a variety, use it as default
    if (widget.requirement.variety != null &&
        widget.requirement.variety!.isNotEmpty) {
      _varietyController.text = widget.requirement.variety!;
    }
  }

  @override
  void dispose() {
    _varietyController.dispose();
    _quantityController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectImage(int imageIndex) async {
    final localizations = AppLocalizations.of(context)!;
    final ImagePicker picker = ImagePicker();
    final ImageSource? source = await showModalBottomSheet<ImageSource>(
      context: context,
      builder:
          (BuildContext context) => SafeArea(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: Icon(Icons.photo_camera),
                  title: Text(localizations.takePhoto),
                  onTap: () => Navigator.pop(context, ImageSource.camera),
                ),
                ListTile(
                  leading: Icon(Icons.photo_library),
                  title: Text(localizations.chooseFromGallery),
                  onTap: () => Navigator.pop(context, ImageSource.gallery),
                ),
              ],
            ),
          ),
    );

    if (source == null) return;

    try {
      final XFile? pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 80, // Reduce quality to ensure compatibility
      );

      if (pickedFile != null) {
        setState(() {
          switch (imageIndex) {
            case 1:
              _primaryImage = File(pickedFile.path);
              break;
            case 2:
              _secondaryImage = File(pickedFile.path);
              break;
            case 3:
              _tertiaryImage = File(pickedFile.path);
              break;
          }
        });
      }
    } catch (e) {
      print("Error processing image: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.errorProcessingImage)),
      );
    }
  }

  Future<void> _selectDate(BuildContext context, int dateType) async {
    DateTime? initialDate;
    DateTime firstDate = DateTime.now().subtract(Duration(days: 30));
    DateTime lastDate = DateTime.now().add(Duration(days: 365));

    switch (dateType) {
      case 1: // Available From
        initialDate = _availableFrom;
        break;
      case 2: // Available Until
        initialDate = _availableUntil ?? _availableFrom.add(Duration(days: 30));
        firstDate =
            _availableFrom; // Available until must be after available from
        break;
      case 3: // Harvest Date
        initialDate =
            _harvestDate ?? DateTime.now().subtract(Duration(days: 7));
        lastDate = DateTime.now().add(
          Duration(days: 1),
        ); // Harvest date is usually in the past
        break;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null) {
      setState(() {
        switch (dateType) {
          case 1:
            _availableFrom = picked;
            // If available until is before the new available from, reset it
            if (_availableUntil != null &&
                _availableUntil!.isBefore(_availableFrom)) {
              _availableUntil = null;
            }
            break;
          case 2:
            _availableUntil = picked;
            break;
          case 3:
            _harvestDate = picked;
            break;
        }
      });
    }
  }

  Future<void> _submitProduce() async {
    final localizations = AppLocalizations.of(context)!;

    if (!_formKey.currentState!.validate()) return;

    if (_primaryImage == null) {
      setState(() {
        _errorMessage = localizations.addPrimaryImage;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Prepare produce data
      final Map<String, dynamic> produceData = {
        'quantity': _quantityController.text,
        'unit_type': widget.requirement.unitType,
        'variety': _varietyController.text,
        'description': _descriptionController.text,
        'available_from': DateFormat('yyyy-MM-dd').format(_availableFrom),
        'notes': 'Created for requirement #${widget.requirement.id}',
      };

      // Add optional fields if available
      if (_availableUntil != null) {
        produceData['available_until'] = DateFormat(
          'yyyy-MM-dd',
        ).format(_availableUntil!);
      }

      if (_harvestDate != null) {
        produceData['harvest_date'] = DateFormat(
          'yyyy-MM-dd',
        ).format(_harvestDate!);
      }

      // Prepare images
      final List<http.MultipartFile> images = [];

      if (_primaryImage != null) {
        final fileExtension = path.extension(_primaryImage!.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final fileName = 'image${DateTime.now().millisecondsSinceEpoch}.jpg';

        images.add(
          http.MultipartFile.fromBytes(
            'primary_image',
            await _primaryImage!.readAsBytes(),
            filename: fileName,
            contentType: MediaType.parse(contentType),
          ),
        );
      }

      if (_secondaryImage != null) {
        final fileExtension =
            path.extension(_secondaryImage!.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final fileName =
            'image${DateTime.now().millisecondsSinceEpoch + 1}.jpg';

        images.add(
          http.MultipartFile.fromBytes(
            'secondary_image',
            await _secondaryImage!.readAsBytes(),
            filename: fileName,
            contentType: MediaType.parse(contentType),
          ),
        );
      }

      if (_tertiaryImage != null) {
        final fileExtension =
            path.extension(_tertiaryImage!.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final fileName =
            'image${DateTime.now().millisecondsSinceEpoch + 2}.jpg';

        images.add(
          http.MultipartFile.fromBytes(
            'tertiary_image',
            await _tertiaryImage!.readAsBytes(),
            filename: fileName,
            contentType: MediaType.parse(contentType),
          ),
        );
      }

      // Create produce and express interest at the same time
      final result = await RequirementService.createProduceForRequirement(
        requirementId: widget.requirement.id,
        produceData: produceData,
        images: images,
      );

      // Show success message and navigate back
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.produceCreatedSuccess),
          backgroundColor: Colors.green,
        ),
      );

      // Close the screen and return to the requirement details
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error creating produce: $_errorMessage');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text(localizations.createProduce)),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Requirement summary card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.fulfillingRequirement,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.assignment,
                                size: 16,
                                color: Colors.grey[700],
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${widget.requirement.cropName}${widget.requirement.variety != null ? " (${widget.requirement.variety})" : ""}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.scale,
                                size: 16,
                                color: Colors.grey[700],
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Required: ${widget.requirement.quantity} ${widget.requirement.unitType}',
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.monetization_on,
                                size: 16,
                                color: Colors.green[700],
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Price: ${NumberFormat.currency(locale: 'en_IN', symbol: '₹').format(widget.requirement.priceOffered)} per ${widget.requirement.unitType}',
                                style: TextStyle(
                                  color: Colors.green[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 24),

                  // Error message if present
                  if (_errorMessage != null)
                    Container(
                      padding: EdgeInsets.all(12),
                      margin: EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[700]),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Produce details section
                  Text(
                    localizations.produceDetails,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  // Variety
                  Text(
                    localizations.varietyOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _varietyController,
                    decoration: InputDecoration(
                      hintText: localizations.varietyHint,
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16),

                  // Quantity
                  Text(
                    localizations.quantity,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _quantityController,
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: InputDecoration(
                      hintText: localizations.enterQuantity,
                      border: OutlineInputBorder(),
                      suffixText: widget.requirement.unitType,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localizations.quantityRequired;
                      }

                      final quantity = double.tryParse(value);
                      if (quantity == null) {
                        return localizations.enterValidNumber;
                      }

                      if (quantity <= 0) {
                        return localizations.quantityGreaterThanZero;
                      }

                      return null;
                    },
                  ),
                  SizedBox(height: 16),

                  // Description
                  Text(
                    localizations.descriptionOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  TextFormField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: localizations.describeProduceHint,
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16),

                  // Date fields section
                  Text(
                    localizations.availabilityDates,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  // Available From date
                  Text(
                    '${localizations.availableFrom} *',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context, 1),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(DateFormat.yMMMd().format(_availableFrom)),
                          Icon(Icons.calendar_today, size: 16),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),

                  // Available Until date (optional)
                  Text(
                    localizations.availableUntilOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context, 2),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _availableUntil != null
                                ? DateFormat.yMMMd().format(_availableUntil!)
                                : localizations.notSpecified,
                          ),
                          Icon(Icons.calendar_today, size: 16),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16),

                  // Harvest Date (optional)
                  Text(
                    localizations.harvestDateOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context, 3),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _harvestDate != null
                                ? DateFormat.yMMMd().format(_harvestDate!)
                                : localizations.notSpecified,
                          ),
                          Icon(Icons.calendar_today, size: 16),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 24),

                  // Images section
                  Text(
                    localizations.productImages,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  // Primary Image (required)
                  Text(
                    localizations.primaryImageRequired,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  _buildImagePickerField(1, _primaryImage, true),
                  SizedBox(height: 16),

                  // Secondary Image (optional)
                  Text(
                    localizations.secondaryImageOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  _buildImagePickerField(2, _secondaryImage, false),
                  SizedBox(height: 16),

                  // Tertiary Image (optional)
                  Text(
                    localizations.tertiaryImageOptional,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  _buildImagePickerField(3, _tertiaryImage, false),
                  SizedBox(height: 32),

                  // Transaction summary
                  Card(
                    elevation: 1,
                    color: Colors.grey[50],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: Colors.grey[200]!),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.transactionSummary,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 12),
                          _buildTransactionRow(
                            'Price per ${widget.requirement.unitType}',
                            NumberFormat.currency(
                              locale: 'en_IN',
                              symbol: '₹',
                            ).format(widget.requirement.priceOffered),
                          ),
                          SizedBox(height: 8),
                          _buildTransactionRow(
                            localizations.quantity,
                            '${_quantityController.text} ${widget.requirement.unitType}',
                          ),
                          Divider(height: 24),
                          _buildTransactionRow(
                            localizations.totalAmount,
                            _calculateTotal(),
                            isBold: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 32),

                  // Submit button
                  Container(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitProduce,
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: Colors.green[700],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child:
                          _isLoading
                              ? CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              )
                              : Text(
                                localizations.createProduceAndExpress,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ),
                  ),

                  SizedBox(height: 24),

                  // Note about the process
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.whatHappensNext,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          localizations.afterSubmittingInfo,
                          style: TextStyle(color: Colors.blue[800]),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImagePickerField(
    int imageIndex,
    File? selectedImage,
    bool isRequired,
  ) {
    final localizations = AppLocalizations.of(context)!;

    if (selectedImage != null) {
      return Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              selectedImage,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Material(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  setState(() {
                    switch (imageIndex) {
                      case 1:
                        _primaryImage = null;
                        break;
                      case 2:
                        _secondaryImage = null;
                        break;
                      case 3:
                        _tertiaryImage = null;
                        break;
                    }
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(Icons.close, size: 20, color: Colors.red[700]),
                ),
              ),
            ),
          ),
        ],
      );
    }

    return InkWell(
      onTap: () => _selectImage(imageIndex),
      child: Container(
        height: 150,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isRequired ? Colors.red : Colors.grey[300]!,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.add_a_photo, size: 48, color: Colors.grey[600]),
              const SizedBox(height: 8),
              Text(
                isRequired
                    ? localizations.requiredTapToAddImage
                    : localizations.tapToAddImage,
                style: TextStyle(color: Colors.grey[600]),
              ),
              if (isRequired)
                Text(
                  localizations.primaryImageRequiredError,
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionRow(
    String label,
    String value, {
    bool isBold = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[700],
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: isBold ? Colors.green[700] : Colors.black87,
          ),
        ),
      ],
    );
  }

  String _calculateTotal() {
    final quantity = double.tryParse(_quantityController.text) ?? 0.0;
    final pricePerUnit = widget.requirement.priceOffered;
    final total = quantity * pricePerUnit;

    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    return priceFormat.format(total);
  }
}
