// lib/screens/query_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'package:intl/intl.dart';

class QueryDetailScreen extends StatefulWidget {
  final int queryId;

  const QueryDetailScreen({Key? key, required this.queryId}) : super(key: key);

  @override
  State<QueryDetailScreen> createState() => _QueryDetailScreenState();
}

class _QueryDetailScreenState extends State<QueryDetailScreen> {
  bool _isLoading = true;
  Map<String, dynamic>? _query;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchQueryDetails();
  }

  // Helper method to build complete image URLs
  // Helper method to build complete image URLs
  String _getFullImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return '';

    // If it's already a full URL, return it directly
    if (imagePath.startsWith('http')) return imagePath;

    // Hardcode the base URL directly - during development this is the most reliable approach
    const String imageBaseUrl = 'http://localhost:3000';

    // Ensure path starts with /
    String path = imagePath;
    if (!path.startsWith('/')) {
      path = '/$path';
    }

    // Combine and return
    return '$imageBaseUrl$path';
  }

  Future<void> _fetchQueryDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.authenticatedRequest(
        '/mobile/queries/${widget.queryId}',
        'GET',
      );

      if (response['status'] == 'success') {
        setState(() {
          _query = response['data'];
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load query details';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _closeQuery(
    BuildContext context,
    AppLocalizations localizations,
  ) async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.closeQueryTitle),
          content: Text(localizations.closeQueryMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(localizations.cancel),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(localizations.close),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    try {
      setState(() {
        _isLoading = true;
      });

      final response = await ApiService.authenticatedRequest(
        '/mobile/queries/${widget.queryId}/close',
        'POST',
      );

      if (response['status'] == 'success') {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.queryClosedSuccess)),
        );

        setState(() {
          if (_query != null) {
            _query!['status'] = 'resolved';
          }
          _isLoading = false;
        });

        // Return true to indicate the list should be refreshed
        Navigator.of(context).pop(true);
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to close query';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.queryDetails),
        actions: [
          if (_query != null &&
              (_query!['status'] == 'open' ||
                  _query!['status'] == 'in_progress'))
            IconButton(
              icon: Icon(Icons.check_circle_outline),
              tooltip: localizations.closeQuery,
              onPressed: () => _closeQuery(context, localizations),
            ),
        ],
      ),
      body: _buildBody(localizations),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchQueryDetails,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_query == null) {
      return Center(child: Text(localizations.queryNotFound));
    }

    final DateTime createdAt = DateTime.parse(_query!['created_at']);
    final String formattedDate = DateFormat.yMMMd().add_jm().format(createdAt);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Query header
          Row(
            children: [
              Expanded(
                child: Text(
                  _query!['title'],
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 8),
              _buildStatusChip(_query!['status'], localizations),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            formattedDate,
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
          ),
          const SizedBox(height: 24),

          // Query description
          Text(
            localizations.description,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _query!['description'],
            style: TextStyle(fontSize: 16, color: Colors.grey[800]),
          ),
          const SizedBox(height: 16),

          // Query image if available
          if (_query!['image_url'] != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _getFullImageUrl(
                      _query!['image_url'],
                    ), // Use the helper method here
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        width: double.infinity,
                        height: 200,
                        color: Colors.grey[200],
                        child: Center(
                          child: CircularProgressIndicator(
                            value:
                                loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      print('Error loading image: $error');
                      return Container(
                        width: double.infinity,
                        height: 200,
                        color: Colors.grey[200],
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                color: Colors.grey[400],
                                size: 48,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Failed to load image',
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),

          // Responses section
          if (_query!['responses'] != null &&
              (_query!['responses'] as List).isNotEmpty) ...[
            Divider(),
            const SizedBox(height: 16),
            Text(
              localizations.responses,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._buildResponses(_query!['responses']),
          ],

          // No responses yet
          if (_query!['responses'] == null ||
              (_query!['responses'] as List).isEmpty) ...[
            const SizedBox(height: 32),
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.question_answer_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    localizations.noResponses,
                    style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  List<Widget> _buildResponses(List responses) {
    return responses.map<Widget>((response) {
      final DateTime createdAt = DateTime.parse(response['created_at']);
      final String formattedDate = DateFormat.yMMMd().add_jm().format(
        createdAt,
      );

      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  response['responder_name'] ?? 'Staff',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Spacer(),
                Text(
                  formattedDate,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(response['response']),

            // Response image if available
            if (response['image_url'] != null) ...[
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  _getFullImageUrl(
                    response['image_url'],
                  ), // Use the helper method here
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      width: double.infinity,
                      height: 150,
                      color: Colors.grey[200],
                      child: Center(
                        child: CircularProgressIndicator(
                          value:
                              loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    print('Error loading response image: $error');
                    return Container(
                      width: double.infinity,
                      height: 150,
                      color: Colors.grey[200],
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      );
    }).toList();
  }

  Widget _buildStatusChip(String status, AppLocalizations localizations) {
    Color chipColor;
    String statusText;

    switch (status) {
      case 'open':
        chipColor = Colors.blue;
        statusText = localizations.statusOpen;
        break;
      case 'in_progress':
        chipColor = Colors.orange;
        statusText = localizations.statusInProgress;
        break;
      case 'resolved':
        chipColor = Colors.green;
        statusText = localizations.statusResolved;
        break;
      case 'closed':
        chipColor = Colors.grey;
        statusText = localizations.statusClosed;
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        border: Border.all(color: chipColor.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
