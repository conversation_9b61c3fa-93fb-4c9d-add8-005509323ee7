// lib/screens/language_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fpo_traders/providers/language_provider.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';

class LanguageSelectionScreen extends StatelessWidget {
  const LanguageSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo placeholder - replace with your actual logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.agriculture,
                    size: 60,
                    color: const Color(0xFF7CB342),
                  ),
                ),
                const SizedBox(height: 40),
                const Text(
                  'Select your Preferred language',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                _buildLanguageButton(context, 'English', 'en'),
                const SizedBox(height: 16),
                _buildLanguageButton(context, 'தமிழ்', 'ta'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageButton(
    BuildContext context,
    String languageName,
    String languageCode,
  ) {
    return ElevatedButton(
      onPressed: () {
        // Set the app locale
        Provider.of<LanguageProvider>(
          context,
          listen: false,
        ).setLocale(Locale(languageCode));

        // Navigate to the login screen
        Navigator.of(context).pushReplacementNamed('/login');
      },
      child: Text(languageName, style: const TextStyle(fontSize: 16)),
    );
  }
}
