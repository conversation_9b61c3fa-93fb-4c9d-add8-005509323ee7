// lib/screens/queries_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/screens/query_detail_screen.dart';
import 'package:fpo_traders/screens/create_query_screen.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

class QueriesScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;
  const QueriesScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<QueriesScreen> createState() => _QueriesScreenState();
}

class _QueriesScreenState extends State<QueriesScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _queries = [];
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  final int _itemsPerPage = 10;

  @override
  void initState() {
    super.initState();
    _fetchQueries();
  }

  // Show logout confirmation dialog
  void _showLogoutDialog(BuildContext context, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(localizations.logout),
              onPressed: () {
                _logout(context);
              },
            ),
          ],
        );
      },
    );
  }

  // Perform logout action
  void _logout(BuildContext context) async {
    // Clear authentication token and other user data
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');

    // Navigate to login screen and clear the navigation stack
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _fetchQueries({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
      });
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.authenticatedRequest(
        '/mobile/queries?page=$_currentPage&limit=$_itemsPerPage',
        'GET',
      );

      if (response['status'] == 'success') {
        setState(() {
          _queries =
              refresh
                  ? List<Map<String, dynamic>>.from(response['data']['queries'])
                  : [
                    ..._queries,
                    ...List<Map<String, dynamic>>.from(
                      response['data']['queries'],
                    ),
                  ];

          _totalPages = response['data']['pagination']['totalPages'];
          _hasMorePages = _currentPage < _totalPages;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load queries';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreQueries() async {
    if (_hasMorePages && !_isLoading) {
      setState(() {
        _currentPage++;
      });
      await _fetchQueries();
    }
  }

  Future<void> _refreshQueries() {
    return _fetchQueries(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    if (widget.isContentOnly) {
      return _buildBody(localizations);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.myQueries),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            tooltip: localizations.logout,
            onPressed: () => _showLogoutDialog(context, localizations),
          ),
        ],
      ),
      body: _buildBody(localizations),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreateQueryScreen()),
          );

          // Refresh the list if a new query was created
          if (result == true) {
            _refreshQueries();
          }
        },
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add),
      ),
      // Use the reusable bottom navigation widget
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 2, // Queries screen is at index 2
        user: widget.user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading && _queries.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null && _queries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshQueries,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_queries.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.question_answer_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                localizations.noQueriesYet,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                localizations.createFirstQuery,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateQueryScreen(),
                    ),
                  );

                  if (result == true) {
                    _refreshQueries();
                  }
                },
                icon: Icon(Icons.add),
                label: Text(localizations.askQuestion),
              ),
            ],
          ),
        ),
      );
    }

    // Stack to allow adding the FAB in content-only mode
    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: _refreshQueries,
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (scrollInfo is ScrollEndNotification &&
                  scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent - 200 &&
                  _hasMorePages) {
                _loadMoreQueries();
              }
              return false;
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: _queries.length + (_hasMorePages ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _queries.length) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final query = _queries[index];
                final DateTime createdAt = DateTime.parse(query['created_at']);
                final String formattedDate = DateFormat.yMMMd().format(
                  createdAt,
                );

                return Card(
                  margin: const EdgeInsets.symmetric(
                    vertical: 6.0,
                    horizontal: 4.0,
                  ),
                  elevation: 1,
                  child: InkWell(
                    onTap: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  QueryDetailScreen(queryId: query['id']),
                        ),
                      );

                      if (result == true) {
                        _refreshQueries();
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  query['title'],
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              _buildStatusChip(query['status'], localizations),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            query['description'],
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Colors.grey[800],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.comment_outlined,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${query['response_count']}',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        // Add FAB only in content-only mode, since the Scaffold already has one otherwise
        if (widget.isContentOnly)
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CreateQueryScreen(),
                  ),
                );

                // Refresh the list if a new query was created
                if (result == true) {
                  _refreshQueries();
                }
              },
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(Icons.add),
            ),
          ),
      ],
    );
  }

  Widget _buildStatusChip(String status, AppLocalizations localizations) {
    Color chipColor;
    String statusText;

    switch (status) {
      case 'open':
        chipColor = Colors.blue;
        statusText = localizations.statusOpen;
        break;
      case 'in_progress':
        chipColor = Colors.orange;
        statusText = localizations.statusInProgress;
        break;
      case 'resolved':
        chipColor = Colors.green;
        statusText = localizations.statusResolved;
        break;
      case 'closed':
        chipColor = Colors.grey;
        statusText = localizations.statusClosed;
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        border: Border.all(color: chipColor.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
