// lib/screens/profile_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/services/firebase_messaging_service.dart';

class ProfileScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const ProfileScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late User _user;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _user = widget.user;
    _fetchProfileDetails();
  }

  Future<void> _fetchProfileDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.authenticatedRequest(
        '/mobile/profile',
        'GET',
      );

      if (response['status'] == 'success') {
        setState(() {
          final userData = response['data']['farmer'];
          _user = User.fromJson(userData);
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load profile';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _logout() async {
    final localizations = AppLocalizations.of(context)!;

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(localizations.cancel),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(localizations.logout),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    try {
      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      // Try to clean up Firebase messaging resources - don't stop logout if this fails
      try {
        await FirebaseMessagingService.cleanUp();
      } catch (e) {
        print('Warning: Error during Firebase cleanup: $e');
        // Continue with logout despite the error
      }

      // Clear user data - this is critical for logout
      try {
        await ApiService.clearUserData();
      } catch (e) {
        print('Error clearing user data: $e');
        // Show an error but still continue with navigation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Some data could not be cleared: $e')),
        );
      }

      // Navigate to login screen - do this regardless of errors above
      Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
    } catch (e) {
      print('Unexpected error during logout: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error during logout: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (widget.isContentOnly) {
      return _buildBody(localizations);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.profile),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            tooltip: localizations.logout,
            onPressed: _logout,
          ),
        ],
      ),
      body: _buildBody(localizations),
      // Use the reusable bottom navigation widget
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 3, // Profile screen is at index 3
        user: _user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchProfileDetails,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile picture
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              shape: BoxShape.circle,
              image:
                  _user.profilePicUrl != null
                      ? DecorationImage(
                        image: NetworkImage(_user.profilePicUrl!),
                        fit: BoxFit.cover,
                      )
                      : null,
            ),
            child:
                _user.profilePicUrl == null
                    ? Icon(Icons.person, size: 80, color: Colors.grey[600])
                    : null,
          ),
          const SizedBox(height: 16),

          // User name
          Text(
            _user.fullName,
            style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),

          // Username
          Text(
            '@${_user.username}',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 32),

          // Contact Information
          _buildInfoSection(localizations.contactInformation, [
            _buildInfoItem(
              Icons.phone,
              localizations.mobileNumber,
              _user.mobileNumber,
            ),
            if (_user.alternateNumber != null &&
                _user.alternateNumber!.isNotEmpty)
              _buildInfoItem(
                Icons.phone_android,
                localizations.alternateNumber,
                _user.alternateNumber!,
              ),
            if (_user.emailId != null && _user.emailId!.isNotEmpty)
              _buildInfoItem(Icons.email, localizations.email, _user.emailId!),
          ]),

          const SizedBox(height: 24),

          // Address Information
          if (_hasAddressInfo())
            _buildInfoSection(localizations.addressInformation, [
              if (_user.addressLine1 != null && _user.addressLine1!.isNotEmpty)
                _buildInfoItem(
                  Icons.home,
                  localizations.addressLine1,
                  _user.addressLine1!,
                ),
              if (_user.addressLine2 != null && _user.addressLine2!.isNotEmpty)
                _buildInfoItem(
                  Icons.home_work,
                  localizations.addressLine2,
                  _user.addressLine2!,
                ),
              if (_user.locality != null && _user.locality!.isNotEmpty)
                _buildInfoItem(
                  Icons.location_on,
                  localizations.locality,
                  _user.locality!,
                ),
              if (_user.pincode != null && _user.pincode!.isNotEmpty)
                _buildInfoItem(
                  Icons.pin_drop,
                  localizations.pincode,
                  _user.pincode!,
                ),
            ]),

          const SizedBox(height: 32),

          // Edit Profile Button
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to edit profile screen
              // This would be implemented in a real app
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Edit Profile functionality would be implemented here',
                  ),
                ),
              );
            },
            icon: Icon(Icons.edit),
            label: Text(localizations.editProfile),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...items,
      ],
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 22, color: Colors.grey[700]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(height: 4),
                Text(value, style: TextStyle(fontSize: 16)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _hasAddressInfo() {
    return (_user.addressLine1 != null && _user.addressLine1!.isNotEmpty) ||
        (_user.addressLine2 != null && _user.addressLine2!.isNotEmpty) ||
        (_user.locality != null && _user.locality!.isNotEmpty) ||
        (_user.pincode != null && _user.pincode!.isNotEmpty);
  }
}
