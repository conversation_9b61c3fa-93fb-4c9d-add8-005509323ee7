// lib/screens/transactions_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/transaction.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/transaction_service.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/screens/transaction_detail_screen.dart';
import 'package:intl/intl.dart';

class TransactionsScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const TransactionsScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;

  List<Transaction> _transactions = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  bool _isLoadingMore = false;
  Map<String, dynamic>? _transactionStats;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);
    _fetchTransactions();
    _fetchTransactionStats();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          !_isLoadingMore &&
          _hasMorePages) {
        _loadMoreTransactions();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  // Helper function to safely parse doubles
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper function to safely parse integers
  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging ||
        _tabController.index != _tabController.previousIndex) {
      setState(() {
        _transactions = [];
        _currentPage = 1;
      });
      _fetchTransactions();
    }
  }

  Future<void> _fetchTransactionStats() async {
    try {
      setState(() {
        _isLoadingStats = true;
      });

      final stats = await TransactionService.getTransactionStats();

      setState(() {
        _transactionStats = stats;
        _isLoadingStats = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingStats = false;
      });
      print('Error fetching transaction stats: $e');
    }
  }

  Future<void> _fetchTransactions({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _transactions = [];
      });
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final result =
          _getCurrentTabType() == 'all'
              ? await TransactionService.getTransactions(
                page: _currentPage,
                limit: 10,
              )
              : await TransactionService.getTransactionsByType(
                type: _getCurrentTabType(),
                page: _currentPage,
                limit: 10,
              );

      final transactions = result['transactions'] as List<Transaction>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        if (refresh) {
          _transactions = transactions;
        } else {
          _transactions.addAll(transactions);
        }

        _totalPages = _parseInt(pagination['totalPages'] ?? 1);
        _hasMorePages = _currentPage < _totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreTransactions() async {
    if (!_hasMorePages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final result =
          _getCurrentTabType() == 'all'
              ? await TransactionService.getTransactions(
                page: _currentPage,
                limit: 10,
              )
              : await TransactionService.getTransactionsByType(
                type: _getCurrentTabType(),
                page: _currentPage,
                limit: 10,
              );

      final transactions = result['transactions'] as List<Transaction>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _transactions.addAll(transactions);
        _totalPages = _parseInt(pagination['totalPages'] ?? 1);
        _hasMorePages = _currentPage < _totalPages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load more transactions')),
        );
      });
    }
  }

  String _getCurrentTabType() {
    switch (_tabController.index) {
      case 0:
        return 'all';
      case 1:
        return 'direct_purchase';
      case 2:
        return 'requirement_based';
      default:
        return 'all';
    }
  }

  Future<void> _refreshTransactions() {
    return _fetchTransactions(refresh: true);
  }

  void _navigateToTransactionDetail(Transaction transaction) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => TransactionDetailScreen(
              transactionId: transaction.id,
              user: widget.user,
            ),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      _refreshTransactions();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (widget.isContentOnly) {
      return _buildContentWithTabs(localizations);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Transactions'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'All'),
            Tab(text: 'Direct Purchase'),
            Tab(text: 'Requirement Based'),
          ],
        ),
      ),
      body: _buildBody(localizations),
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 3, // Update this based on your navigation indices
        user: widget.user,
      ),
    );
  }

  Widget _buildContentWithTabs(AppLocalizations localizations) {
    return Column(
      children: [
        // Stats summary card
        if (!_isLoadingStats && _transactionStats != null)
          _buildTransactionStatsCard(),

        // Tabs for content-only mode
        Container(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          child: TabBar(
            controller: _tabController,
            tabs: [
              Tab(text: 'All'),
              Tab(text: 'Direct Purchase'),
              Tab(text: 'Requirement Based'),
            ],
            labelColor: Theme.of(context).primaryColor,
            indicatorColor: Theme.of(context).primaryColor,
          ),
        ),

        // Main content - expanded to fill available space
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshTransactions,
            child: _buildTransactionsList(localizations),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    return Column(
      children: [
        // Stats summary card
        if (!_isLoadingStats && _transactionStats != null)
          _buildTransactionStatsCard(),

        // Transactions list (takes remaining space)
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshTransactions,
            child: _buildTransactionsList(localizations),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionStatsCard() {
    final totals = _transactionStats!['totals'];
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );

    return Card(
      margin: EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction Summary',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Transactions',
                    _parseInt(totals['total_transactions'] ?? 0).toString(),
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Completed',
                    _parseInt(totals['completed_transactions'] ?? 0).toString(),
                    Colors.green,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Value',
                    priceFormat.format(
                      _parseDouble(totals['total_value'] ?? 0),
                    ),
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Completed Value',
                    priceFormat.format(
                      _parseDouble(totals['completed_value'] ?? 0),
                    ),
                    Colors.teal,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(label, style: TextStyle(color: Colors.grey[700], fontSize: 14)),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsList(AppLocalizations localizations) {
    if (_isLoading && _transactions.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError && _transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              'Failed to load transactions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshTransactions,
              child: Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              'No transactions found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              _getEmptyStateMessage(),
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16),
      itemCount: _transactions.length + (_hasMorePages ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _transactions.length) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final transaction = _transactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  String _getEmptyStateMessage() {
    switch (_getCurrentTabType()) {
      case 'all':
        return 'You don\'t have any transactions yet.';
      case 'direct_purchase':
        return 'You don\'t have any direct purchase transactions.';
      case 'requirement_based':
        return 'You don\'t have any requirement-based transactions.';
      default:
        return 'No transactions found.';
    }
  }

  Widget _buildTransactionCard(Transaction transaction) {
    // Format the date
    final DateTime createdAt = DateTime.parse(transaction.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);

    // Format price
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    // Get status details
    final statusDetails = transaction.getStatusDetails();
    final statusColor = _getStatusColor(statusDetails['color'] as String);

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: InkWell(
        onTap: () => _navigateToTransactionDetail(transaction),
        child: Column(
          children: [
            // Header with crop and status
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  // Crop image if available
                  if (transaction.produceImage != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        transaction.produceImage!,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 50,
                            height: 50,
                            color: Colors.grey[300],
                            child: Icon(Icons.image, color: Colors.grey[500]),
                          );
                        },
                      ),
                    )
                  else
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(Icons.crop, color: Colors.grey[500]),
                    ),

                  SizedBox(width: 12),

                  // Crop details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          transaction.cropName,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4),
                        if (transaction.variety != null)
                          Text(
                            transaction.variety!,
                            style: TextStyle(
                              fontSize: 14,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Status indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      border: Border.all(color: statusColor.withOpacity(0.5)),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      statusDetails['label'] as String,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Transaction details
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Transaction type and date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        transaction.transactionTypeDisplay,
                        style: TextStyle(color: Colors.grey[700], fontSize: 14),
                      ),
                      Text(
                        formattedDate,
                        style: TextStyle(color: Colors.grey[700], fontSize: 14),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),

                  // Quantity
                  Row(
                    children: [
                      Icon(
                        Icons.inventory_2,
                        size: 16,
                        color: Colors.grey[700],
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Quantity: ${transaction.quantity} ${transaction.unitType}',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),

                  // Price
                  Row(
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 16,
                        color: Colors.green[700],
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Price: ${priceFormat.format(transaction.pricePerUnit)} per ${transaction.unitType}',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),

                  // Total amount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        priceFormat.format(transaction.totalAmount),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Action button if needed
            if (_canUpdateStatus(transaction.status))
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: OutlinedButton(
                  onPressed: () => _navigateToTransactionDetail(transaction),
                  child: Text(_getButtonTextForStatus(transaction.status)),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Theme.of(context).primaryColor),
                  ),
                ),
              ),

            SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'green':
        return Colors.green[700]!;
      case 'blue':
        return Colors.blue[700]!;
      case 'orange':
        return Colors.orange[700]!;
      case 'red':
        return Colors.red[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  bool _canUpdateStatus(String status) {
    // Check if the farmer can update the status based on current status
    switch (status) {
      case 'accepted':
      case 'in_progress':
        return true;
      default:
        return false;
    }
  }

  String _getButtonTextForStatus(String status) {
    switch (status) {
      case 'accepted':
        return 'View Details';
      case 'in_progress':
        return 'Mark as Delivered';
      default:
        return 'View Details';
    }
  }
}
