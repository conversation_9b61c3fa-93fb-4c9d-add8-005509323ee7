// lib/screens/home_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/models/knowledge_article.dart';
import 'package:fpo_traders/models/announcement.dart';
import 'package:flutter_carousel_slider/carousel_slider.dart';
import 'package:fpo_traders/widgets/price_history_chart.dart';
import 'package:fpo_traders/widgets/recent_transactions_widget.dart';
import 'package:fpo_traders/widgets/transaction_stats_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/services/knowledge_service.dart';
import 'package:fpo_traders/services/announcement_service.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'package:fpo_traders/screens/knowledge_article_detail_screen.dart';
import 'package:fpo_traders/screens/announcement_detail_screen.dart';
import 'package:fpo_traders/screens/requirements/requirements_list_screen.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const HomeScreen({Key? key, required this.user, this.isContentOnly = false})
    : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = true;
  List<KnowledgeArticle> _articles = [];
  List<Announcement> _announcements = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  Future<void> _fetchData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Fetch announcements and articles in parallel
      final announcementsFuture = AnnouncementService.getAnnouncements(
        page: 1,
        limit: 3,
      );
      final articlesFuture = KnowledgeService.getArticles(page: 1, limit: 6);

      // Wait for both to complete
      final results = await Future.wait([announcementsFuture, articlesFuture]);

      final announcements = results[0]['announcements'] as List<Announcement>;
      final articles = results[1]['articles'] as List<KnowledgeArticle>;

      setState(() {
        _announcements = announcements;
        _articles = articles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error fetching data: $_errorMessage');
    }
  }

  // Show logout confirmation dialog
  void _showLogoutDialog(BuildContext context, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(localizations.logout),
              onPressed: () {
                _logout(context);
              },
            ),
          ],
        );
      },
    );
  }

  // Perform logout action
  void _logout(BuildContext context) async {
    // Clear authentication token and other user data
    await _clearUserData();

    // Navigate to login screen and clear the navigation stack
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  // Clear user data from shared preferences
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    // Clear any other stored user data if needed
  }

  // Navigate to requirements screen
  void _navigateToRequirements() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RequirementsListScreen(user: widget.user),
      ),
    );
  }

  void _showDebugDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Debug Info'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Base URL:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(ApiService.baseUrl),
                  SizedBox(height: 8),
                  Text(
                    'Base URL without path:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(ApiService.baseUrlWithoutPath),
                  SizedBox(height: 16),
                  Text(
                    'Announcements:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ..._announcements.asMap().entries.map(
                    (entry) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${entry.key + 1}. ${entry.value.title}:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(entry.value.imageUrl ?? 'No image URL'),
                        SizedBox(height: 8),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Close'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get localization instance
    final localizations = AppLocalizations.of(context)!;

    // Prepare the body content
    Widget bodyContent =
        _isLoading
            ? Center(child: CircularProgressIndicator())
            : _errorMessage != null
            ? _buildErrorWidget()
            : _buildHomeContent(localizations);

    // If in content-only mode, just return the body content
    if (widget.isContentOnly) {
      return bodyContent;
    }

    // Otherwise return the full Scaffold with AppBar and BottomNavigationBar
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.agriculture,
                size: 18,
                color: const Color(0xFF7CB342),
              ),
            ),
            const SizedBox(width: 10),
            Text(localizations.welcomeUser(widget.user.fullName)),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            tooltip: localizations.logout,
            onPressed: () => _showLogoutDialog(context, localizations),
          ),
        ],
      ),
      body: bodyContent,
      floatingActionButton:
          kDebugMode ||
                  kIsWeb // Show in debug mode or web
              ? FloatingActionButton(
                onPressed: _showDebugDialog,
                child: Icon(Icons.bug_report),
                backgroundColor: Colors.red,
                mini: true,
              )
              : null,
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 0, // Home screen is at index 0
        user: widget.user,
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
          SizedBox(height: 16),
          Text(
            'Failed to load content',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              _errorMessage ?? 'An error occurred. Please try again.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(onPressed: _fetchData, child: Text('Try Again')),
        ],
      ),
    );
  }

  Widget _buildHomeContent(AppLocalizations localizations) {
    // Select banner announcements (first 3 with images)
    final bannerAnnouncements =
        _announcements
            .where(
              (announcement) =>
                  announcement.imageUrl != null &&
                  announcement.imageUrl!.isNotEmpty,
            )
            .take(3)
            .toList();

    // If not enough announcements with images, use ones without images
    if (bannerAnnouncements.length < 3) {
      final remainingAnnouncements =
          _announcements
              .where(
                (announcement) => !bannerAnnouncements.contains(announcement),
              )
              .take(3 - bannerAnnouncements.length)
              .toList();

      bannerAnnouncements.addAll(remainingAnnouncements);
    }

    // If still not enough announcements, use fallback data
    if (bannerAnnouncements.isEmpty) {
      bannerAnnouncements.add(
        Announcement(
          id: 0,
          title: "Welcome to FPO TRADERS",
          description: "Connect farmers with markets and get the best prices.",
          createdAt: DateTime.now().toString(),
          createdByName: "Admin",
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _fetchData,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Banner slider with announcements
            Container(
              height: 180,
              child: CarouselSlider.builder(
                slideBuilder: (index) {
                  final announcement = bannerAnnouncements[index];
                  return GestureDetector(
                    onTap: () {
                      if (announcement.id > 0) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => AnnouncementDetailScreen(
                                  announcementId: announcement.id,
                                ),
                          ),
                        );
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 5.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.grey[300],
                      ),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child:
                                announcement.imageUrl != null
                                    ? Image.network(
                                      announcement.imageUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          color: Colors.grey[300],
                                          child: Icon(
                                            Icons.campaign,
                                            size: 50,
                                            color: Colors.grey[500],
                                          ),
                                        );
                                      },
                                    )
                                    : Container(
                                      color: Colors.grey[300],
                                      child: Icon(
                                        Icons.campaign,
                                        size: 50,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withOpacity(0.7),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 15,
                            left: 15,
                            right: 15,
                            child: Text(
                              announcement.title,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                slideTransform: CubeTransform(),
                slideIndicator: CircularSlideIndicator(
                  padding: EdgeInsets.only(bottom: 10),
                  indicatorRadius: 4,
                  itemSpacing: 15,
                  indicatorBackgroundColor: Colors.grey,
                  currentIndicatorColor: Colors.white,
                ),
                itemCount: bannerAnnouncements.length,
                autoSliderTransitionTime: Duration(milliseconds: 500),
                autoSliderDelay: Duration(seconds: 5),
                enableAutoSlider: false,
              ),
            ),

            // FPO Requirements Button
            Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 8.0),
              child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: InkWell(
                  onTap: _navigateToRequirements,
                  child: Container(
                    padding: EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.assignment_outlined,
                            color: Colors.green[800],
                            size: 28,
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Active FPO Requirements',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'View and respond to crop requirements from FPOs',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(Icons.arrow_forward_ios, size: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            SizedBox(height: 24),

            // Transaction activity widget
            TransactionStatsWidget(user: widget.user),

            SizedBox(height: 24),

            // Recent transactions widget
            RecentTransactionsWidget(user: widget.user, limit: 3),

            SizedBox(height: 32),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Latest articles section
                  Text(
                    localizations.latestArticles,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  // Get latest 3 articles
                  _articles.isEmpty
                      ? Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 20.0),
                          child: Text(
                            'No articles available',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ),
                      )
                      : Column(
                        children:
                            _articles
                                .take(3)
                                .map((article) => _buildArticleCard(article))
                                .toList(),
                      ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleCard(KnowledgeArticle article) {
    // Format date
    final DateTime createdAt = DateTime.parse(article.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);

    return GestureDetector(
      onTap: () {
        // Navigate to article detail
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    KnowledgeArticleDetailScreen(articleId: article.id),
          ),
        );
      },
      child: Card(
        elevation: 2,
        margin: EdgeInsets.only(bottom: 16),
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Article image
            if (article.imageUrl != null)
              Container(
                height: 150,
                width: double.infinity,
                color: Colors.grey[300],
                child: Image.network(
                  article.imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Icon(
                        Icons.image,
                        size: 48,
                        color: Colors.grey[500],
                      ),
                    );
                  },
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    article.title,
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8),
                  Text(
                    article.description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        formattedDate,
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      Row(
                        children: [
                          Icon(Icons.person, size: 14, color: Colors.grey[600]),
                          SizedBox(width: 4),
                          Text(
                            article.createdByName,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
