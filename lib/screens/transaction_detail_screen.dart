// lib/screens/transaction_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/transaction.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/transaction_service.dart';
import 'package:intl/intl.dart';

class TransactionDetailScreen extends StatefulWidget {
  final int transactionId;
  final User user;

  const TransactionDetailScreen({
    Key? key,
    required this.transactionId,
    required this.user,
  }) : super(key: key);

  @override
  State<TransactionDetailScreen> createState() =>
      _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends State<TransactionDetailScreen> {
  bool _isLoading = true;
  bool _isUpdating = false;
  Transaction? _transaction;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadTransactionDetails();
  }

  // Helper function to safely parse doubles
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper function to safely parse integers
  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  Future<void> _loadTransactionDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final transaction = await TransactionService.getTransactionDetails(
        widget.transactionId,
      );

      setState(() {
        _transaction = transaction;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _updateTransactionStatus(String newStatus) async {
    // Show confirmation dialog before proceeding
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Confirm Status Update'),
            content: Text(
              'Are you sure you want to mark this transaction as $newStatus?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('Confirm'),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
    );

    if (confirm != true) return;

    try {
      setState(() {
        _isUpdating = true;
      });

      final success = await TransactionService.updateTransactionStatus(
        widget.transactionId,
        newStatus,
      );

      setState(() {
        _isUpdating = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Transaction status updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        // Reload transaction details to reflect the changes
        await _loadTransactionDetails();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update transaction status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isUpdating = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text('Transaction Details')),
      body: _buildBody(localizations),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              'Failed to load transaction details',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadTransactionDetails,
              child: Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_transaction == null) {
      return Center(child: Text('Transaction not found'));
    }

    // Get details for UI presentation
    final transaction = _transaction!;
    final statusDetails = transaction.getStatusDetails();
    final statusColor = _getStatusColor(statusDetails['color'] as String);

    // Format dates
    final DateTime createdAt = DateTime.parse(transaction.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);
    final String formattedTime = DateFormat.jm().format(createdAt);

    // Format price
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 2,
    );

    // Debug print for image URL
    print('Transaction produce image URL: ${transaction.produceImage}');

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildStatusIndicator(statusColor),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Status: ${statusDetails['label']}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              statusDetails['description'] as String,
                              style: TextStyle(color: Colors.grey[700]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  if (statusDetails['next_step'] != null) ...[
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue[700]),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              statusDetails['next_step'] as String,
                              style: TextStyle(color: Colors.blue[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Status update buttons
                  if (_canUpdateStatus(transaction.status)) ...[
                    SizedBox(height: 16),
                    _isUpdating
                        ? Center(child: CircularProgressIndicator())
                        : _buildStatusUpdateButton(transaction.status),
                  ],
                ],
              ),
            ),
          ),

          SizedBox(height: 24),

          // Transaction details card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Transaction Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  // Transaction type
                  _buildInfoRow(
                    'Transaction Type',
                    transaction.transactionTypeDisplay,
                  ),
                  SizedBox(height: 12),

                  // Date
                  _buildInfoRow(
                    'Date & Time',
                    '$formattedDate at $formattedTime',
                  ),
                  SizedBox(height: 12),

                  // Transaction ID
                  _buildInfoRow('Transaction ID', '#${transaction.id}'),

                  Divider(height: 32),

                  // Product details
                  Text(
                    'Product Details',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  // Crop/Produce details with image if available
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Image
                      if (transaction.produceImage != null)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            transaction.produceImage!,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              print('Error loading image: $error');
                              print('Image URL: ${transaction.produceImage}');
                              return Container(
                                width: 80,
                                height: 80,
                                color: Colors.grey[300],
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey[500],
                                    ),
                                    Text(
                                      'Image Error',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        )
                      else
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.crop, color: Colors.grey[500]),
                        ),

                      SizedBox(width: 16),

                      // Details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              transaction.cropName,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 4),
                            if (transaction.variety != null)
                              Text(
                                'Variety: ${transaction.variety}',
                                style: TextStyle(color: Colors.grey[800]),
                              ),
                            SizedBox(height: 8),
                            Text(
                              'Quantity: ${transaction.quantity} ${transaction.unitType}',
                              style: TextStyle(color: Colors.grey[800]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  Divider(height: 32),

                  // Price breakdown
                  Text(
                    'Price Details',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),

                  _buildPriceRow(
                    'Price per Unit',
                    '${priceFormat.format(transaction.pricePerUnit)} / ${transaction.unitType}',
                  ),
                  SizedBox(height: 8),

                  _buildPriceRow(
                    'Quantity',
                    '${transaction.quantity} ${transaction.unitType}',
                  ),

                  Divider(height: 24, thickness: 1),

                  _buildPriceRow(
                    'Total Amount',
                    priceFormat.format(transaction.totalAmount),
                    isBold: true,
                  ),
                ],
              ),
            ),
          ),

          // Inventory transactions if available
          if (transaction.inventoryTransactions != null &&
              transaction.inventoryTransactions!.isNotEmpty) ...[
            SizedBox(height: 24),
            _buildInventoryTransactionsCard(transaction.inventoryTransactions!),
          ],

          SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(Color color) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(color: color, shape: BoxShape.circle),
      child: Icon(Icons.check, color: Colors.white, size: 16),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(label, style: TextStyle(color: Colors.grey[700])),
        ),
        Expanded(
          flex: 3,
          child: Text(value, style: TextStyle(fontWeight: FontWeight.w500)),
        ),
      ],
    );
  }

  Widget _buildPriceRow(String label, String value, {bool isBold = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: isBold ? Colors.black : Colors.grey[700],
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            fontSize: isBold ? 18 : 16,
            color: isBold ? Colors.green[700] : Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryTransactionsCard(
    List<Map<String, dynamic>> inventoryTransactions,
  ) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Inventory Updates',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),

            ...inventoryTransactions.map((transaction) {
              final DateTime transactionDate = DateTime.parse(
                transaction['created_at'] as String,
              );
              final String formattedDate = DateFormat.yMMMd().format(
                transactionDate,
              );
              final String formattedTime = DateFormat.jm().format(
                transactionDate,
              );

              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getInventoryTransactionIcon(
                          transaction['transaction_type'] as String?,
                        ),
                        color: Colors.blue[700],
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getInventoryTransactionTitle(
                              transaction['transaction_type'] as String?,
                            ),
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 4),
                          Text(
                            transaction['notes'] as String? ??
                                'No additional notes',
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '$formattedDate at $formattedTime',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  IconData _getInventoryTransactionIcon(String? transactionType) {
    switch (transactionType?.toLowerCase()) {
      case 'in':
        return Icons.add_box;
      case 'out':
        return Icons.indeterminate_check_box;
      case 'adjustment':
        return Icons.change_circle;
      default:
        return Icons.sync;
    }
  }

  String _getInventoryTransactionTitle(String? transactionType) {
    switch (transactionType?.toLowerCase()) {
      case 'in':
        return 'Added to Inventory';
      case 'out':
        return 'Removed from Inventory';
      case 'adjustment':
        return 'Inventory Adjustment';
      default:
        return 'Inventory Update';
    }
  }

  Color _getStatusColor(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'green':
        return Colors.green[700]!;
      case 'blue':
        return Colors.blue[700]!;
      case 'orange':
        return Colors.orange[700]!;
      case 'red':
        return Colors.red[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  bool _canUpdateStatus(String status) {
    // Only show update button for specific statuses where farmer action is expected
    return status == 'in_progress';
  }

  Widget _buildStatusUpdateButton(String currentStatus) {
    if (currentStatus == 'in_progress') {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => _updateTransactionStatus('delivered'),
          child: Text('Mark as Delivered'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[700],
            padding: EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      );
    }

    return Container(); // Return an empty container for statuses that don't need an action button
  }
}
