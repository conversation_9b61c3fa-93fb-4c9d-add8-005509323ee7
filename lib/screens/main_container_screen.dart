// lib/screens/main_container_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/screens/farmer_produce_screen.dart';
import 'package:fpo_traders/screens/home_screen.dart';
import 'package:fpo_traders/screens/knowledge_screen.dart';
import 'package:fpo_traders/screens/market_price_screen.dart';
import 'package:fpo_traders/screens/profile_screen.dart';
import 'package:fpo_traders/screens/queries_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MainContainerScreen extends StatefulWidget {
  final User user;
  final int initialIndex;

  const MainContainerScreen({
    Key? key,
    required this.user,
    this.initialIndex = 0,
  }) : super(key: key);

  @override
  State<MainContainerScreen> createState() => _MainContainerScreenState();
}

class _MainContainerScreenState extends State<MainContainerScreen> {
  late int _currentIndex;
  late PageController _pageController;
  final List<Widget> _mainScreens = []; // Screens for bottom navigation
  late Widget _profileScreen; // Profile screen shown separately

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);

    // Initialize screens - profile is handled separately
    _mainScreens.addAll([
      HomeScreen(user: widget.user, isContentOnly: true),
      KnowledgeScreen(user: widget.user, isContentOnly: true),
      FarmerProduceScreen(
        user: widget.user,
        isContentOnly: true,
      ), // Add this line
      QueriesScreen(user: widget.user, isContentOnly: true),
      MarketPriceScreen(user: widget.user, isContentOnly: true),
    ]);

    // Initialize profile screen
    _profileScreen = ProfileScreen(user: widget.user, isContentOnly: true);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    setState(() {
      _currentIndex = index;
    });
  }

  void _showProfile(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ProfileScreen(user: widget.user)),
    );
  }

  Future<void> _logout(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _showLogoutDialog(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.logoutConfirmation),
          content: Text(localizations.logoutConfirmationMessage),
          actions: [
            TextButton(
              child: Text(localizations.cancel),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(localizations.logout),
              onPressed: () {
                Navigator.of(context).pop();
                _logout(context);
              },
            ),
          ],
        );
      },
    );
  }

  String _getScreenTitle(int index, AppLocalizations localizations) {
    switch (index) {
      case 0:
        return localizations.appTitle;
      case 1:
        return localizations.knowledge;
      case 2:
        return localizations.myProduce; // Title for Produce screen
      case 3:
        return localizations.queries;
      case 4:
        return localizations.marketPrices;
      default:
        return localizations.appTitle;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(_getScreenTitle(_currentIndex, localizations)),
        actions: [
          // Add profile picture dropdown menu
          PopupMenuButton<String>(
            offset: const Offset(0, 50),
            icon: CircleAvatar(
              radius: 16,
              backgroundImage:
                  widget.user.profilePicUrl != null
                      ? NetworkImage(widget.user.profilePicUrl!)
                      : null,
              child:
                  widget.user.profilePicUrl == null
                      ? Text(widget.user.fullName[0].toUpperCase())
                      : null,
            ),
            onSelected: (value) {
              if (value == 'profile') {
                _showProfile(context);
              } else if (value == 'logout') {
                _showLogoutDialog(context, localizations);
              }
            },
            itemBuilder:
                (BuildContext context) => [
                  PopupMenuItem<String>(
                    value: 'profile',
                    child: Row(
                      children: [
                        Icon(
                          Icons.person,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(localizations.profile),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  PopupMenuItem<String>(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(
                          Icons.logout,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(localizations.logout),
                      ],
                    ),
                  ),
                ],
          ),
          const SizedBox(width: 12),
        ],
      ),
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        physics:
            const NeverScrollableScrollPhysics(), // Disable swiping between screens
        children: _mainScreens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: localizations.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.menu_book),
            label: localizations.knowledge,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.agriculture), // Changed icon
            label: 'Produce', // New label for Produce
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.question_answer),
            label: localizations.queries,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.trending_up),
            label: localizations.marketPrices,
          ),
        ],
      ),
    );
  }
}
