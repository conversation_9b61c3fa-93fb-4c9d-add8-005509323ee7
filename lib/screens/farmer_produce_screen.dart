// lib/screens/farmer_produce_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/farmer_produce_service.dart';
import 'package:fpo_traders/screens/create_edit_produce_screen.dart';
import 'package:fpo_traders/screens/produce_detail_screen.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/widgets/image_debug_widget.dart'; // Import our widget

// The helper function is removed as we'll use the built-in Flutter localization
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/farmer_produce_service.dart';
import 'package:fpo_traders/screens/create_edit_produce_screen.dart';
import 'package:fpo_traders/screens/produce_detail_screen.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/widgets/bottom_navigation_widget.dart';
import 'package:fpo_traders/widgets/image_debug_widget.dart'; // Import our widget

class FarmerProduceScreen extends StatefulWidget {
  final User user;
  final bool isContentOnly;

  const FarmerProduceScreen({
    Key? key,
    required this.user,
    this.isContentOnly = false,
  }) : super(key: key);

  @override
  State<FarmerProduceScreen> createState() => _FarmerProduceScreenState();
}

class _FarmerProduceScreenState extends State<FarmerProduceScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;

  List<FarmerProduce> _produceList = [];
  List<FarmerProduce> _filteredProduceList = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMorePages = false;
  bool _isLoadingMore = false;
  String _currentFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _fetchProduce();

    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoading &&
          !_isLoadingMore &&
          _hasMorePages) {
        _loadMoreProduce();
      }
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging ||
        _tabController.index != _tabController.previousIndex) {
      setState(() {
        switch (_tabController.index) {
          case 0:
            _currentFilter = 'all';
            break;
          case 1:
            _currentFilter = 'available';
            break;
          case 2:
            _currentFilter = 'pending';
            break;
          case 3:
            _currentFilter = 'expired';
            break;
        }
        _filterProduceList();
      });
    }
  }

  void _filterProduceList() {
    if (_currentFilter == 'all') {
      _filteredProduceList = List.from(_produceList);
    } else {
      _filteredProduceList =
          _produceList
              .where(
                (produce) =>
                    produce.status.toLowerCase() ==
                    _currentFilter.toLowerCase(),
              )
              .toList();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchProduce({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _produceList = [];
      });
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final result = await FarmerProduceService.getMyProduce(
        page: _currentPage,
        limit: 10,
      );

      final produce = result['produce'] as List<FarmerProduce>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        if (refresh) {
          _produceList = produce;
        } else {
          _produceList.addAll(produce);
        }

        _filterProduceList();

        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreProduce() async {
    if (!_hasMorePages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final result = await FarmerProduceService.getMyProduce(
        page: _currentPage,
        limit: 10,
      );

      final produce = result['produce'] as List<FarmerProduce>;
      final pagination = result['pagination'] as Map<String, dynamic>;

      setState(() {
        _produceList.addAll(produce);
        _filterProduceList();

        _totalPages = pagination['totalPages'];
        _hasMorePages = _currentPage < _totalPages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        final localizations = AppLocalizations.of(context)!;
        print(
          "Current locale: ${Localizations.localeOf(context).languageCode}",
        );
        print("myProduce translated value: ${localizations.myProduce}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.failedToLoadMoreProduce)),
        );
      });
    }
  }

  Future<void> _refreshProduce() {
    return _fetchProduce(refresh: true);
  }

  void _navigateToCreateProduce() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => CreateEditProduceScreen()),
    );

    if (result == true) {
      _refreshProduce();
    }
  }

  void _navigateToEditProduce(FarmerProduce produce) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateEditProduceScreen(produce: produce),
      ),
    );

    if (result == true) {
      _refreshProduce();
    }
  }

  void _navigateToProduceDetails(FarmerProduce produce) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProduceDetailScreen(produceId: produce.id),
      ),
    );
  }

  // Method to show image viewer in full screen
  void _showFullScreenImage(BuildContext context, String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullScreenImageViewer(imageUrl: imageUrl),
      ),
    );
  }

  Future<void> _updateProduceStatus(
    FarmerProduce produce,
    String newStatus,
  ) async {
    final localizations = AppLocalizations.of(context)!;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text(localizations.updateProduceStatus),
              ],
            ),
          ),
        );
      },
    );

    try {
      final success = await FarmerProduceService.updateProduceStatus(
        produce.id,
        newStatus,
      );

      // Close the dialog
      Navigator.of(context).pop();

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.produceStatusUpdated)),
        );
        _refreshProduce(); // Refresh the list to show the updated status
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.failedToUpdateStatus)),
        );
      }
    } catch (e) {
      // Close the dialog
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${localizations.errorOccurred}: ${e.toString()}'),
        ),
      );
    }
  }

  Future<void> _deleteProduce(FarmerProduce produce) async {
    final localizations = AppLocalizations.of(context)!;

    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations.deleteProduceTitle),
            content: Text(localizations.deleteProduceConfirm),
            actions: [
              TextButton(
                child: Text(localizations.cancel),
                onPressed: () => Navigator.of(context).pop(false),
              ),
              TextButton(
                child: Text(localizations.delete),
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text(localizations.deleting),
              ],
            ),
          ),
        );
      },
    );

    try {
      final success = await FarmerProduceService.deleteProduce(produce.id);

      // Close the dialog
      Navigator.of(context).pop();

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.produceDeletedSuccess)),
        );
        _refreshProduce();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.failedToDeleteProduce)),
        );
      }
    } catch (e) {
      // Close the dialog
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${localizations.errorOccurred}: ${e.toString()}'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    // Prepare the body content
    Widget bodyContent = _buildBody(localizations);

    // If in content-only mode, just return the body content
    if (widget.isContentOnly) {
      return Stack(
        children: [
          bodyContent,
          Positioned(
            right: 16,
            bottom: 16,
            child: FloatingActionButton(
              onPressed: _navigateToCreateProduce,
              backgroundColor: Theme.of(context).primaryColor,
              child: Icon(Icons.add),
            ),
          ),
        ],
      );
    }

    // Otherwise return the full Scaffold with the centered FAB
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.myProduce),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: localizations.all),
            Tab(text: localizations.available),
            Tab(text: localizations.pending),
            Tab(text: localizations.expired),
          ],
        ),
      ),
      body: bodyContent,
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateProduce,
        child: Icon(Icons.add),
        tooltip: localizations.addNewProduce,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: BottomNavigationWidget(
        currentIndex: 2, // Assuming produce is at index 2
        user: widget.user,
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading && _produceList.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError && _produceList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              localizations.failedToLoadProduce,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? localizations.errorOccurred,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _refreshProduce,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_produceList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.agriculture_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              localizations.noProduceListings,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              localizations.addFirstProduce,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToCreateProduce,
              icon: Icon(Icons.add),
              label: Text(localizations.addProduce),
            ),
          ],
        ),
      );
    }

    if (_filteredProduceList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.filter_list, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              "No $_currentFilter produce listings",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              localizations.changeFilterOrAdd,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _tabController.animateTo(0),
              child: Text(localizations.showAllProduce),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshProduce,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16),
        itemCount: _filteredProduceList.length + (_hasMorePages ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredProduceList.length) {
            return Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final produce = _filteredProduceList[index];
          return _buildProduceCard(produce);
        },
      ),
    );
  }

  Widget _buildProduceCard(FarmerProduce produce) {
    final localizations = AppLocalizations.of(context)!;
    final DateTime createdAt = DateTime.parse(produce.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);
    final DateTime availableFrom = DateTime.parse(produce.availableFrom);
    final String formattedAvailableFrom = DateFormat.yMMMd().format(
      availableFrom,
    );

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToProduceDetails(produce),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Produce image - Using our modified widget with tappable functionality
            Stack(
              children: [
                GestureDetector(
                  onTap: () {
                    if (produce.primaryImageUrl != null) {
                      _showFullScreenImage(context, produce.primaryImageUrl!);
                    }
                  },
                  child: NetworkImageWithDebug(
                    imageUrl: produce.primaryImageUrl,
                    height: 160,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: Center(child: CircularProgressIndicator()),
                    errorWidget: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            size: 48,
                            color: Colors.grey[500],
                          ),
                          SizedBox(height: 8),
                          Text(
                            localizations.longPressToDebug,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Status indicator
                Positioned(
                  top: 10,
                  right: 10,
                  child: _buildStatusChip(produce.status),
                ),
                // Add zoom indicator
                if (produce.primaryImageUrl != null)
                  Positioned(
                    bottom: 10,
                    right: 10,
                    child: Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(Icons.zoom_in, color: Colors.white, size: 16),
                    ),
                  ),
              ],
            ),

            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and actions
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          produce.cropName,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      PopupMenuButton<String>(
                        icon: Icon(Icons.more_vert),
                        onSelected: (value) {
                          if (value == 'edit') {
                            _navigateToEditProduce(produce);
                          } else if (value == 'delete') {
                            _deleteProduce(produce);
                          } else if (value.startsWith('mark_')) {
                            final newStatus = value.substring(5);
                            _updateProduceStatus(produce, newStatus);
                          }
                        },
                        itemBuilder:
                            (context) => [
                              PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit, size: 18),
                                    SizedBox(width: 8),
                                    Text(localizations.edit),
                                  ],
                                ),
                              ),
                              // Status update options
                              if (produce.status.toLowerCase() == 'available' ||
                                  produce.status.toLowerCase() == 'pending')
                                PopupMenuItem(
                                  value: 'mark_expired',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.warning_amber,
                                        size: 18,
                                        color: Colors.orange,
                                      ),
                                      SizedBox(width: 8),
                                      Text(localizations.markAsExpired),
                                    ],
                                  ),
                                ),
                              if (produce.status.toLowerCase() == 'available')
                                PopupMenuItem(
                                  value: 'mark_sold',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.monetization_on,
                                        size: 18,
                                        color: Colors.blue,
                                      ),
                                      SizedBox(width: 8),
                                      Text(localizations.markAsSold),
                                    ],
                                  ),
                                ),
                              if (produce.status.toLowerCase() == 'expired')
                                PopupMenuItem(
                                  value: 'mark_available',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.check_circle,
                                        size: 18,
                                        color: Colors.green,
                                      ),
                                      SizedBox(width: 8),
                                      Text(localizations.markAsAvailable),
                                    ],
                                  ),
                                ),
                              PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.delete,
                                      size: 18,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      localizations.delete,
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                      ),
                    ],
                  ),
                  SizedBox(height: 8),

                  // Variety if available
                  if (produce.variety != null && produce.variety!.isNotEmpty)
                    Text(
                      produce.variety!,
                      style: TextStyle(
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                      ),
                    ),

                  SizedBox(height: 12),

                  // Quantity and price
                  Row(
                    children: [
                      Icon(
                        Icons.line_weight,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${produce.quantity} ${produce.unitType}',
                        style: TextStyle(fontSize: 16),
                      ),
                    ],
                  ),

                  SizedBox(height: 8),

                  // Availability date
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        "Available from $formattedAvailableFrom",
                        style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                      ),
                    ],
                  ),

                  SizedBox(height: 16),

                  // Footer with date and interest count
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Posted: $formattedDate",
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      Row(
                        children: [
                          Icon(Icons.people, size: 16, color: Colors.grey[600]),
                          SizedBox(width: 4),
                          Text(
                            "${produce.interestCount} interested",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final localizations = AppLocalizations.of(context)!;
    Color chipColor;
    String statusText;

    switch (status.toLowerCase()) {
      case 'available':
        chipColor = Colors.green;
        statusText = localizations.available;
        break;
      case 'pending':
        chipColor = Colors.orange;
        statusText = localizations.pending;
        break;
      case 'sold':
        chipColor = Colors.blue;
        statusText = localizations.sold;
        break;
      case 'expired':
        chipColor = Colors.grey;
        statusText = localizations.expired;
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildBulkActionButton() {
    final localizations = AppLocalizations.of(context)!;
    return ElevatedButton.icon(
      onPressed: () {
        // Show bulk action dialog
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(localizations.bulkUpdate),
              content: Text(localizations.bulkUpdateQuestion),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(localizations.cancel),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // Implement bulk update logic here
                  },
                  child: Text(localizations.update),
                ),
              ],
            );
          },
        );
      },
      icon: Icon(Icons.edit),
      label: Text(localizations.bulkUpdate),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.secondary,
      ),
    );
  }
}

// Full-screen image viewer widget
class _FullScreenImageViewer extends StatelessWidget {
  final String imageUrl;

  const _FullScreenImageViewer({Key? key, required this.imageUrl})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Center(
        child: InteractiveViewer(
          panEnabled: true,
          boundaryMargin: EdgeInsets.all(20),
          minScale: 0.5,
          maxScale: 4,
          child: Image.network(
            imageUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  color: Colors.white,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.broken_image, color: Colors.white, size: 64),
                    SizedBox(height: 16),
                    Text(
                      localizations.failedToLoadImage,
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
