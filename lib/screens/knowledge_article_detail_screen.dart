// lib/screens/knowledge_article_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/knowledge_article.dart';
import 'package:fpo_traders/services/knowledge_service.dart';
import 'package:intl/intl.dart';

class KnowledgeArticleDetailScreen extends StatefulWidget {
  final int articleId;

  const KnowledgeArticleDetailScreen({Key? key, required this.articleId})
    : super(key: key);

  @override
  State<KnowledgeArticleDetailScreen> createState() =>
      _KnowledgeArticleDetailScreenState();
}

class _KnowledgeArticleDetailScreenState
    extends State<KnowledgeArticleDetailScreen> {
  late Future<KnowledgeArticle> _articleFuture;
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();
    _loadArticle();
    // Record view when opening the article
    _recordView();
  }

  Future<void> _recordView() async {
    try {
      await KnowledgeService.recordView(widget.articleId);
    } catch (e) {
      // Silently fail as view recording is not critical
    }
  }

  void _loadArticle() {
    _articleFuture = KnowledgeService.getArticleById(widget.articleId);
    _articleFuture.then((article) {
      setState(() {
        _isLiked = article.isLiked;
      });
    });
  }

  Future<void> _toggleLike() async {
    try {
      final isLiked = await KnowledgeService.toggleLike(widget.articleId);
      setState(() {
        _isLiked = isLiked;
      });
      // Refresh article to get updated stats
      _loadArticle();
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to update like status')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.knowledge),
        actions: [
          IconButton(
            icon: Icon(_isLiked ? Icons.favorite : Icons.favorite_border),
            color: _isLiked ? Colors.red : null,
            onPressed: _toggleLike,
            tooltip: _isLiked ? 'Unlike' : 'Like',
          ),
          IconButton(
            icon: Icon(Icons.share),
            onPressed: () {
              // Share functionality would be implemented here
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Share functionality to be implemented'),
                ),
              );
            },
            tooltip: 'Share',
          ),
        ],
      ),
      body: FutureBuilder<KnowledgeArticle>(
        future: _articleFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error loading article',
                    style: TextStyle(color: Colors.red),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _loadArticle();
                      });
                    },
                    child: Text('Try Again'),
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData) {
            return Center(child: Text('Article not found'));
          }

          final article = snapshot.data!;
          final DateTime createdAt = DateTime.parse(article.createdAt);
          final String formattedDate = DateFormat.yMMMd().format(createdAt);

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Article image
                if (article.imageUrl != null)
                  Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(color: Colors.grey[300]),
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        Positioned.fill(
                          child: Image.network(
                            article.imageUrl!,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                color: Colors.grey[300],
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value:
                                        loadingProgress.expectedTotalBytes !=
                                                null
                                            ? loadingProgress
                                                    .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                            : null,
                                  ),
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              print('Error loading image: $error');
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.image_not_supported,
                                      size: 48,
                                      color: Colors.grey[500],
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Image unavailable',
                                      style: TextStyle(color: Colors.grey[500]),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Article metadata
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 4),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(width: 12),
                          Icon(Icons.person, size: 16, color: Colors.grey[600]),
                          SizedBox(width: 4),
                          Text(
                            article.createdByName,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      // Stats
                      Row(
                        children: [
                          Icon(Icons.favorite, size: 16, color: Colors.red),
                          SizedBox(width: 4),
                          Text(
                            article.likeCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(width: 12),
                          Icon(
                            Icons.visibility,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          SizedBox(width: 4),
                          Text(
                            article.viewCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),

                      // Article title
                      Text(
                        article.title,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16),

                      // Article content
                      Text(
                        article.description,
                        style: TextStyle(fontSize: 16, height: 1.5),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
