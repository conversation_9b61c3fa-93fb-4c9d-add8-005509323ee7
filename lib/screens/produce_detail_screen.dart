// lib/screens/produce_detail_screen.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/services/farmer_produce_service.dart';
import 'package:intl/intl.dart';

class ProduceDetailScreen extends StatefulWidget {
  final int produceId;

  const ProduceDetailScreen({Key? key, required this.produceId})
    : super(key: key);

  @override
  State<ProduceDetailScreen> createState() => _ProduceDetailScreenState();
}

class _ProduceDetailScreenState extends State<ProduceDetailScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  FarmerProduce? _produce;

  @override
  void initState() {
    super.initState();
    _loadProduceDetails();
  }

  Future<void> _loadProduceDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });

      final produce = await FarmerProduceService.getProduceDetails(
        widget.produceId,
      );

      setState(() {
        _produce = produce;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text('Produce Details')),
      body: _buildBody(localizations),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            SizedBox(height: 16),
            Text(
              'Failed to load produce details',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                _errorMessage ?? 'An error occurred. Please try again.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadProduceDetails,
              child: Text(localizations.tryAgain),
            ),
          ],
        ),
      );
    }

    if (_produce == null) {
      return Center(child: Text('Produce not found'));
    }

    // Format dates
    final DateTime createdAt = DateTime.parse(_produce!.createdAt);
    final String formattedCreatedAt = DateFormat.yMMMd().format(createdAt);
    final DateTime availableFrom = DateTime.parse(_produce!.availableFrom);
    final String formattedAvailableFrom = DateFormat.yMMMd().format(
      availableFrom,
    );

    String? formattedAvailableUntil;
    if (_produce!.availableUntil != null &&
        _produce!.availableUntil!.isNotEmpty) {
      final DateTime availableUntil = DateTime.parse(_produce!.availableUntil!);
      formattedAvailableUntil = DateFormat.yMMMd().format(availableUntil);
    }

    String? formattedHarvestDate;
    if (_produce!.harvestDate != null && _produce!.harvestDate!.isNotEmpty) {
      final DateTime harvestDate = DateTime.parse(_produce!.harvestDate!);
      formattedHarvestDate = DateFormat.yMMMd().format(harvestDate);
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(
          16,
        ).copyWith(bottom: 32), // Increased bottom padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Produce Image
            if (_produce!.primaryImageUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  height: 200,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Image.network(
                          _produce!.primaryImageUrl!,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(child: CircularProgressIndicator());
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.image,
                                color: Colors.grey[500],
                                size: 48,
                              ),
                            );
                          },
                        ),
                      ),
                      // Status indicator
                      Positioned(
                        top: 10,
                        right: 10,
                        child: _buildStatusChip(_produce!.status),
                      ),
                    ],
                  ),
                ),
              ),
            SizedBox(height: 20),

            // Produce Name and Variety
            Text(
              _produce!.cropName,
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            if (_produce!.variety != null && _produce!.variety!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  _produce!.variety!,
                  style: TextStyle(
                    fontSize: 18,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            SizedBox(height: 16),

            // Quantity
            _buildInfoRow(
              Icons.line_weight,
              'Quantity',
              '${_produce!.quantity} ${_produce!.unitType}',
            ),
            SizedBox(height: 12),

            // Availability
            _buildInfoRow(
              Icons.calendar_today,
              'Available From',
              formattedAvailableFrom,
            ),
            if (formattedAvailableUntil != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: _buildInfoRow(
                  Icons.event_busy,
                  'Available Until',
                  formattedAvailableUntil,
                ),
              ),
            SizedBox(height: 12),

            // Harvest Date
            if (formattedHarvestDate != null)
              _buildInfoRow(
                Icons.agriculture,
                'Harvest Date',
                formattedHarvestDate,
              ),
            SizedBox(height: 16),

            // Description
            if (_produce!.description != null &&
                _produce!.description!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Description',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    _produce!.description!,
                    style: TextStyle(fontSize: 16, color: Colors.grey[800]),
                  ),
                  SizedBox(height: 20),
                ],
              ),

            // Additional images
            if (_produce!.secondaryImageUrl != null ||
                _produce!.tertiaryImageUrl != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Additional Images',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 12),
                  Row(
                    children: [
                      if (_produce!.secondaryImageUrl != null)
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              height: 100,
                              color: Colors.grey[300],
                              child: Image.network(
                                _produce!.secondaryImageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.image,
                                      color: Colors.grey[500],
                                      size: 24,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      if (_produce!.secondaryImageUrl != null &&
                          _produce!.tertiaryImageUrl != null)
                        SizedBox(width: 12),
                      if (_produce!.tertiaryImageUrl != null)
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              height: 100,
                              color: Colors.grey[300],
                              child: Image.network(
                                _produce!.tertiaryImageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.image,
                                      color: Colors.grey[500],
                                      size: 24,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              ),

            // Posted date and interest count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Posted: $formattedCreatedAt',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                Row(
                  children: [
                    Icon(Icons.people, size: 16, color: Colors.grey[600]),
                    SizedBox(width: 4),
                    Text(
                      '${_produce!.interestCount} interested',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),

            // Extra bottom space to ensure everything is visible when scrolling
            SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[700]),
        SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 4),
            Text(value, style: TextStyle(fontSize: 16)),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    String statusText;

    switch (status.toLowerCase()) {
      case 'available':
        chipColor = Colors.green;
        statusText = 'Available';
        break;
      case 'pending':
        chipColor = Colors.orange;
        statusText = 'Pending';
        break;
      case 'sold':
        chipColor = Colors.blue;
        statusText = 'Sold';
        break;
      case 'expired':
        chipColor = Colors.grey;
        statusText = 'Expired';
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
