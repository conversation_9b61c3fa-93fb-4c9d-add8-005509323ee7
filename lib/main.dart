// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:fpo_traders/screens/market_price_screen.dart';
import 'package:provider/provider.dart';
import 'package:fpo_traders/providers/language_provider.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:fpo_traders/services/firebase_messaging_service.dart';

// Import screens
import 'package:fpo_traders/screens/splash_screen.dart';
import 'package:fpo_traders/screens/login_screen.dart';
import 'package:fpo_traders/screens/language_selection_screen.dart';

// Global navigator key for notification navigation
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  // Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();

  print("===== APP INITIALIZATION STARTED =====");

  // Initialize providers first (lightweight)
  final languageProvider = LanguageProvider();

  // Start Firebase initialization in background without waiting
  _initializeFirebaseAsync();

  // Start the app immediately without waiting for Firebase
  print("Starting app UI...");
  runApp(
    MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => languageProvider)],
      child: const MyApp(),
    ),
  );
}

// Initialize Firebase in a separate function to avoid blocking UI startup
// Initialize Firebase in a separate function to avoid blocking UI startup
Future<void> _initializeFirebaseAsync() async {
  try {
    // Initialize Firebase Core first
    await Firebase.initializeApp();

    // Give UI time to fully render before any permission prompts
    await Future.delayed(const Duration(milliseconds: 800));

    // Initialize messaging service which will handle permissions appropriately
    FirebaseMessagingService.initialize();
  } catch (e) {
    print("ERROR during Firebase initialization: $e");
  }
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  get user => null;

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return MaterialApp(
          title: 'FPO TRADERS',
          debugShowCheckedModeBanner: false,

          // Use the global navigator key for notification navigation
          navigatorKey: navigatorKey,

          // Localization setup
          locale: languageProvider.locale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'), // English
            Locale('ta'), // Tamil
          ],

          // App theme
          theme: ThemeData(
            primaryColor: const Color(0xFF7CB342),
            colorScheme: ColorScheme.fromSeed(
              seedColor: const Color(0xFF7CB342),
              primary: const Color(0xFF7CB342),
            ),
            useMaterial3: true,
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF7CB342),
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            appBarTheme: const AppBarTheme(
              centerTitle: false,
              elevation: 0,
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
            ),
          ),

          // Define named routes
          initialRoute: '/',
          routes: {
            '/': (context) => const SplashScreen(),
            '/language': (context) => const LanguageSelectionScreen(),
            '/login': (context) => const LoginScreen(),
            '/market-prices': (context) => MarketPriceScreen(user: user),
          },

          // Handle notification navigation
          onGenerateRoute: (settings) {
            if (settings.name != null &&
                settings.name!.startsWith('/notification')) {
              // Extract any parameters from the route
              final args = settings.arguments;
              // Handle navigation based on notification data
              // You can add custom logic here based on your notification structure
            }
            return null;
          },
        );
      },
    );
  }
}
