// lib/models/knowledge_article.dart
import 'package:fpo_traders/services/api_service.dart';

class KnowledgeArticle {
  final int id;
  final String title;
  final String description;
  final String? imageUrl;
  final String createdAt;
  final String createdByName;
  final int likeCount;
  final int viewCount;
  final bool isLiked;

  KnowledgeArticle({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.createdAt,
    required this.createdByName,
    this.likeCount = 0,
    this.viewCount = 0,
    this.isLiked = false,
  });

  factory KnowledgeArticle.fromJson(Map<String, dynamic> json) {
    // Process image URL to ensure it's a full URL
    String? imageUrl = json['image_url'];
    if (imageUrl != null && imageUrl.isNotEmpty) {
      // If image URL doesn't start with http or https, prepend the base URL
      if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
        final baseUrl =
            imageUrl.startsWith('/')
                ? ApiService.baseUrlWithoutPath
                : '${ApiService.baseUrlWithoutPath}/';
        imageUrl = '$baseUrl$imageUrl';
      }
    }

    return KnowledgeArticle(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imageUrl: imageUrl,
      createdAt: json['created_at'],
      createdByName: json['created_by_name'] ?? 'Unknown',
      likeCount: json['like_count'] ?? 0,
      viewCount: json['view_count'] ?? 0,
      isLiked: json['isLiked'] ?? false,
    );
  }
}
