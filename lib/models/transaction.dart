// lib/models/transaction.dart
import 'package:fpo_traders/services/api_service.dart';

class Transaction {
  final int id;
  final String transactionType;
  final String transactionTypeDisplay;
  final String status;
  final double quantity;
  final String unitType;
  final double pricePerUnit;
  final double totalAmount;
  final String createdAt;
  final String? produceImage;
  final String cropName;
  final String? variety;
  final Map<String, dynamic>? statusInfo;
  final List<Map<String, dynamic>>? inventoryTransactions;

  Transaction({
    required this.id,
    required this.transactionType,
    required this.transactionTypeDisplay,
    required this.status,
    required this.quantity,
    required this.unitType,
    required this.pricePerUnit,
    required this.totalAmount,
    required this.createdAt,
    this.produceImage,
    required this.cropName,
    this.variety,
    this.statusInfo,
    this.inventoryTransactions,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse doubles
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    // Helper function to safely parse integers
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.round();
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }

    // Process image URL similar to how it's done in Requirement model
    String? processImageUrl(String? url) {
      if (url == null || url.isEmpty) {
        return null;
      }

      // If it's already a full URL
      if (url.startsWith('http://') || url.startsWith('https://')) {
        // Replace localhost with proper IP for mobile testing
        final baseUrlWithoutPath = ApiService.baseUrlWithoutPath;
        if (url.contains('localhost:3000')) {
          return url.replaceAll('http://localhost:3000', baseUrlWithoutPath);
        }
        return url;
      }

      // If it's a relative path, prepend the base URL
      final baseUrl =
          url.startsWith('/')
              ? ApiService.baseUrlWithoutPath
              : '${ApiService.baseUrlWithoutPath}/';
      return '$baseUrl$url';
    }

    return Transaction(
      id: parseInt(json['id']),
      transactionType: json['transaction_type'] ?? 'direct_purchase',
      transactionTypeDisplay:
          json['transaction_type_display'] ?? 'Direct Purchase',
      status: json['status'] ?? 'pending',
      quantity: parseDouble(json['quantity']),
      unitType: json['unit_type'] ?? 'kg',
      pricePerUnit: parseDouble(json['price_per_unit']),
      totalAmount: parseDouble(json['total_amount']),
      createdAt: json['created_at'] ?? '',
      produceImage: processImageUrl(json['produce_image']),
      cropName: json['crop_name'] ?? 'Unknown Crop',
      variety: json['variety'],
      statusInfo:
          json['status_info'] != null
              ? Map<String, dynamic>.from(json['status_info'])
              : null,
      inventoryTransactions:
          json['inventory_transactions'] != null
              ? List<Map<String, dynamic>>.from(json['inventory_transactions'])
              : null,
    );
  }

  // Method to get status color as a Material color
  Map<String, dynamic> getStatusDetails() {
    if (statusInfo != null) {
      return statusInfo!;
    }

    // Default status info if not provided by API
    final Map<String, Map<String, dynamic>> defaultStatusMap = {
      'accepted': {
        'label': 'Accepted',
        'color': 'blue',
        'description': 'Transaction has been accepted and is in progress',
        'next_step': 'Wait for FPO to progress the transaction',
      },
      'in_progress': {
        'label': 'In Progress',
        'color': 'orange',
        'description': 'FPO is processing your produce',
        'next_step': 'Deliver your produce as requested',
      },
      'delivered': {
        'label': 'Delivered',
        'color': 'green',
        'description': 'Produce has been delivered to FPO',
        'next_step': 'Wait for FPO to confirm and close the transaction',
      },
      'closed': {
        'label': 'Closed',
        'color': 'green',
        'description': 'Transaction completed successfully',
        'next_step': 'Transaction is complete',
      },
      'cancelled': {
        'label': 'Cancelled',
        'color': 'red',
        'description': 'Transaction was cancelled',
        'next_step': 'No further action needed',
      },
    };

    return defaultStatusMap[status] ??
        {
          'label': status.substring(0, 1).toUpperCase() + status.substring(1),
          'color': 'gray',
          'description': 'Status information not available',
          'next_step': 'Contact FPO for more information',
        };
  }
}
