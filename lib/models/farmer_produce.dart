// lib/models/farmer_produce.dart
import 'package:fpo_traders/services/api_service.dart';

class FarmerProduce {
  final int id;
  final int productId;
  final String cropName;
  final String? variety;
  final double quantity;
  final String unitType;
  final String? description;
  final String? primaryImageUrl;
  final String? secondaryImageUrl;
  final String? tertiaryImageUrl;
  final String? harvestDate;
  final String availableFrom;
  final String? availableUntil;
  final String status;
  final String createdAt;
  final int interestCount;

  FarmerProduce({
    required this.id,
    required this.productId,
    required this.cropName,
    this.variety,
    required this.quantity,
    required this.unitType,
    this.description,
    this.primaryImageUrl,
    this.secondaryImageUrl,
    this.tertiaryImageUrl,
    this.harvestDate,
    required this.availableFrom,
    this.availableUntil,
    required this.status,
    required this.createdAt,
    this.interestCount = 0,
  });

  factory FarmerProduce.fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert any value to double
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (e) {
          print('Error parsing string to double: $value');
          return 0.0;
        }
      }
      return 0.0;
    }

    // Helper function to safely convert any value to int
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.round();
      if (value is String) {
        try {
          return int.parse(value);
        } catch (e) {
          print('Error parsing string to int: $value');
          return 0;
        }
      }
      return 0;
    }

    // Process image URLs to ensure they're full URLs
    String? processImageUrl(String? url) {
      if (url == null || url.isEmpty) {
        return null;
      }

      // If it's already a full URL, return it as is
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // Make sure the URL starts with a slash if needed
      final String adjustedUrl = url.startsWith('/') ? url : '/$url';

      // Combine with base URL
      final String fullUrl = '${ApiService.baseUrlWithoutPath}$adjustedUrl';
      print('Processed URL: $fullUrl');
      return fullUrl;
    }

    return FarmerProduce(
      id: parseInt(json['id']),
      productId: parseInt(json['product_id']),
      cropName: json['crop_name'] ?? 'Unknown',
      variety: json['variety'],
      quantity: parseDouble(json['quantity']),
      unitType: json['unit_type'] ?? 'kg',
      description: json['description'],
      primaryImageUrl: processImageUrl(json['primary_image_url']),
      secondaryImageUrl: processImageUrl(json['secondary_image_url']),
      tertiaryImageUrl: processImageUrl(json['tertiary_image_url']),
      harvestDate: json['harvest_date'],
      availableFrom: json['available_from'],
      availableUntil: json['available_until'],
      status: json['status'] ?? 'available',
      createdAt: json['created_at'],
      interestCount: parseInt(json['interest_count']),
    );
  }
}
