// lib/models/announcement.dart
import 'package:fpo_traders/services/api_service.dart';

class Announcement {
  final int id;
  final String title;
  final String description;
  final String? imageUrl;
  final String createdAt;
  final String createdByName;

  Announcement({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.createdAt,
    required this.createdByName,
  });

  factory Announcement.fromJson(Map<String, dynamic> json) {
    // Process image URL to ensure it's a full URL
    String? imageUrl = json['image_url'];
    print('Raw announcement image URL: $imageUrl');
    if (imageUrl != null && imageUrl.isNotEmpty) {
      // If it's already a full URL from the API
      if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        // If it's from localhost and we're testing on a physical device, replace with the actual IP
        final baseUrlWithoutPath = ApiService.baseUrlWithoutPath;
        print('Base URL: $baseUrlWithoutPath');
        if (imageUrl.contains('localhost:3000')) {
          imageUrl = imageUrl.replaceAll(
            'http://localhost:3000',
            baseUrlWithoutPath,
          );
          print('Replaced URL: $imageUrl');
        }
      } else {
        // If it's a relative path, prepend the base URL
        final baseUrl =
            imageUrl.startsWith('/')
                ? ApiService.baseUrlWithoutPath
                : '${ApiService.baseUrlWithoutPath}/';
        imageUrl = '$baseUrl$imageUrl';
      }
    }

    print('Final processed URL: $imageUrl');

    return Announcement(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imageUrl: imageUrl,
      createdAt: json['created_at'],
      createdByName: json['created_by_name'] ?? 'Admin',
    );
  }
}
