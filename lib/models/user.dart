// lib/models/user.dart
class User {
  final int id;
  final String username;
  final String fullName;
  final String mobileNumber;
  final String? profilePicUrl;
  final String? alternateNumber;
  final String? emailId;
  final String? addressLine1;
  final String? addressLine2;
  final String? locality;
  final String? pincode;
  final String? cityName;
  final String? stateName;

  User({
    required this.id,
    required this.username,
    required this.fullName,
    required this.mobileNumber,
    this.profilePicUrl,
    this.alternateNumber,
    this.emailId,
    this.addressLine1,
    this.addressLine2,
    this.locality,
    this.pincode,
    this.cityName,
    this.stateName,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      fullName: json['full_name'],
      mobileNumber: json['mobile_number'],
      profilePicUrl: json['profile_pic_url'],
      alternateNumber: json['alternate_number'],
      emailId: json['email_id'],
      addressLine1: json['address_line1'],
      addressLine2: json['address_line2'],
      locality: json['locality'],
      pincode: json['pincode'],
      cityName: json['city_name'],
      stateName: json['state_name'],
    );
  }
}
