// lib/models/market_price.dart
import 'package:fpo_traders/services/api_service.dart';

class MarketPrice {
  final int productId;
  final String cropName;
  final String? cropImageUrl;
  final double pricePerUnit;
  final String unitType;
  final String priceDate;
  final double avgMonthPrice;
  final double maxMonthPrice;
  final double minMonthPrice;

  MarketPrice({
    required this.productId,
    required this.cropName,
    this.cropImageUrl,
    required this.pricePerUnit,
    required this.unitType,
    required this.priceDate,
    this.avgMonthPrice = 0,
    this.maxMonthPrice = 0,
    this.minMonthPrice = 0,
  });

  factory MarketPrice.fromJson(Map<String, dynamic> json) {
    // Process image URL to ensure it's a full URL
    String? cropImageUrl = json['crop_image_url'];
    if (cropImageUrl != null && cropImageUrl.isNotEmpty) {
      // If image URL doesn't start with http or https, prepend the base URL
      if (!cropImageUrl.startsWith('http://') &&
          !cropImageUrl.startsWith('https://')) {
        final baseUrl =
            cropImageUrl.startsWith('/')
                ? ApiService.baseUrlWithoutPath
                : '${ApiService.baseUrlWithoutPath}/';
        cropImageUrl = '$baseUrl$cropImageUrl';
      }
    }

    // Helper function to parse doubles from either strings or numbers
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? 0.0;
      return 0.0;
    }

    return MarketPrice(
      productId:
          json['product_id'] is String
              ? int.parse(json['product_id'])
              : json['product_id'],
      cropName: json['crop_name'] ?? 'Unknown',
      cropImageUrl: cropImageUrl,
      pricePerUnit: parseDouble(json['price_per_unit']),
      unitType: json['unit_type'] ?? 'kg',
      priceDate: json['price_date'] ?? '',
      avgMonthPrice: parseDouble(json['avg_month_price']),
      maxMonthPrice: parseDouble(json['max_month_price']),
      minMonthPrice: parseDouble(json['min_month_price']),
    );
  }
}
