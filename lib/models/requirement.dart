// lib/models/requirement.dart
import 'package:fpo_traders/services/api_service.dart';

class Requirement {
  final int id;
  final int productId;
  final String cropName;
  final String? cropImageUrl;
  final String? variety;
  final double quantity;
  final String unitType;
  final double priceOffered;
  final String? requiredBy;
  final String status;
  final String? description;
  final String createdAt;
  final String createdByName;
  final double? marketPriceAtCreation;
  final String? marketPriceUnit;
  final int interestCount;
  final double? availableQuantity;
  final bool hasExpressedInterest;
  final Map<String, dynamic>? interestDetails;

  Requirement({
    required this.id,
    required this.productId,
    required this.cropName,
    this.cropImageUrl,
    this.variety,
    required this.quantity,
    required this.unitType,
    required this.priceOffered,
    this.requiredBy,
    required this.status,
    this.description,
    required this.createdAt,
    required this.createdByName,
    this.marketPriceAtCreation,
    this.marketPriceUnit,
    this.interestCount = 0,
    this.availableQuantity,
    this.hasExpressedInterest = false,
    this.interestDetails,
  });

  factory Requirement.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse doubles
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (e) {
          print('Error parsing value to double: $value');
          return 0.0;
        }
      }
      return 0.0;
    }

    // Helper function to safely parse integers
    int parseInt(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is double) return value.round();
      if (value is String) {
        try {
          return int.parse(value);
        } catch (e) {
          print('Error parsing value to int: $value');
          return 0;
        }
      }
      return 0;
    }

    // Process image URL
    String? cropImageUrl = json['crop_image_url'];
    if (cropImageUrl != null && cropImageUrl.isNotEmpty) {
      // Check if it's already a full URL from the API
      if (cropImageUrl.startsWith('http://') ||
          cropImageUrl.startsWith('https://')) {
        // If it's from localhost and we're testing on a physical device, replace with the actual IP
        final baseUrlWithoutPath = ApiService.baseUrlWithoutPath;
        if (cropImageUrl.contains('localhost:3000')) {
          cropImageUrl = cropImageUrl.replaceAll(
            'http://localhost:3000',
            baseUrlWithoutPath,
          );
        }
      } else {
        // If it's a relative path, prepend the base URL
        final baseUrl =
            cropImageUrl.startsWith('/')
                ? ApiService.baseUrlWithoutPath
                : '${ApiService.baseUrlWithoutPath}/';
        cropImageUrl = '$baseUrl$cropImageUrl';
      }
    }

    print('Final processed cropImageUrl: $cropImageUrl');

    return Requirement(
      id: parseInt(json['id']),
      productId: parseInt(json['product_id']),
      cropName: json['crop_name'] ?? 'Unknown Crop',
      cropImageUrl: cropImageUrl,
      variety: json['variety'],
      quantity: parseDouble(json['quantity']),
      unitType: json['unit_type'] ?? 'kg',
      priceOffered: parseDouble(json['price_offered']),
      requiredBy: json['required_by'],
      status: json['status'] ?? 'active',
      description: json['description'],
      createdAt: json['created_at'] ?? DateTime.now().toIso8601String(),
      createdByName: json['created_by_name'] ?? 'Unknown',
      marketPriceAtCreation:
          json['market_price_at_creation'] != null
              ? parseDouble(json['market_price_at_creation'])
              : null,
      marketPriceUnit: json['market_price_unit'],
      interestCount: parseInt(json['interest_count']),
      availableQuantity:
          json['available_quantity'] != null
              ? parseDouble(json['available_quantity'])
              : null,
      hasExpressedInterest: json['hasExpressedInterest'] ?? false,
      interestDetails: json['interestDetails'],
    );
  }

  // Helper method to get price difference
  double? getPriceDifference() {
    if (marketPriceAtCreation == null) return null;
    return priceOffered - marketPriceAtCreation!;
  }

  // Helper method to check if the price is favorable to the farmer
  bool isPriceFavorable() {
    final difference = getPriceDifference();
    return difference != null && difference > 0;
  }
}
