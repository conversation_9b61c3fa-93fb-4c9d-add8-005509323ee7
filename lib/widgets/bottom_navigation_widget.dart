// Updated bottom_navigation_widget.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/screens/home_screen.dart';
import 'package:fpo_traders/screens/knowledge_screen.dart';
import 'package:fpo_traders/screens/market_price_screen.dart';
import 'package:fpo_traders/screens/queries_screen.dart';
import 'package:fpo_traders/screens/profile_screen.dart';
import 'package:fpo_traders/screens/farmer_produce_screen.dart';
import 'package:fpo_traders/screens/transactions_screen.dart'; // Import the new transactions screen

class BottomNavigationWidget extends StatelessWidget {
  final int currentIndex;
  final User user;

  const BottomNavigationWidget({
    Key? key,
    required this.currentIndex,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Theme.of(context).primaryColor,
      currentIndex: currentIndex,
      onTap: (index) => _onItemTapped(context, index),
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: localizations.home,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.menu_book),
          label: localizations.knowledge,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.agriculture),
          label: localizations.myProduce, // Changed from hardcoded 'Produce'
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.receipt_long),
          label:
              localizations
                  .transactions, // Changed from hardcoded 'Transactions'
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.trending_up),
          label: localizations.marketPrices,
        ),
      ],
    );
  }

  void _onItemTapped(BuildContext context, int index) {
    if (index == currentIndex) return;

    switch (index) {
      case 0: // Home
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => HomeScreen(user: user)),
        );
        break;
      case 1: // Knowledge
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => KnowledgeScreen(user: user)),
        );
        break;
      case 2: // Produce
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => FarmerProduceScreen(user: user),
          ),
        );
        break;
      case 3: // Transactions (new)
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionsScreen(user: user),
          ),
        );
        break;
      case 4: // Market Prices
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => MarketPriceScreen(user: user),
          ),
        );
        break;
    }
  }
}
