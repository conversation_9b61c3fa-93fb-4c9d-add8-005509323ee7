import 'package:flutter/material.dart';
import 'package:fpo_traders/models/transaction.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/services/transaction_service.dart';
import 'package:fpo_traders/screens/transaction_detail_screen.dart';
import 'package:fpo_traders/screens/transactions_screen.dart';
import 'package:intl/intl.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';

class RecentTransactionsWidget extends StatefulWidget {
  final User user;
  final Function()? onViewAllPressed;
  final int limit;

  const RecentTransactionsWidget({
    Key? key,
    required this.user,
    this.onViewAllPressed,
    this.limit = 3,
  }) : super(key: key);

  @override
  State<RecentTransactionsWidget> createState() =>
      _RecentTransactionsWidgetState();
}

class _RecentTransactionsWidgetState extends State<RecentTransactionsWidget> {
  bool _isLoading = true;
  List<Transaction> _transactions = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchRecentTransactions();
  }

  Future<void> _fetchRecentTransactions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final transactions = await TransactionService.getRecentTransactions(
        limit: widget.limit,
      );

      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _navigateToTransactionDetail(Transaction transaction) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => TransactionDetailScreen(
              transactionId: transaction.id,
              user: widget.user,
            ),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      _fetchRecentTransactions();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and view all button
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localizations.recentTransactions,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                TextButton(
                  onPressed:
                      widget.onViewAllPressed ??
                      () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    TransactionsScreen(user: widget.user),
                          ),
                        );
                      },
                  child: Text(
                    localizations.viewAll,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(50, 30),
                  ),
                ),
              ],
            ),
          ),

          // Transactions list or status message
          _buildContent(),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final localizations = AppLocalizations.of(context)!;

    if (_isLoading) {
      return Container(
        height: 150,
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.error_outline, color: Colors.red[400], size: 32),
              SizedBox(height: 12),
              Text(
                localizations.failedToLoadTransactions,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red[800],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _fetchRecentTransactions,
                icon: Icon(Icons.refresh, size: 18),
                label: Text(localizations.retry),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_transactions.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.receipt_long, color: Colors.grey[400], size: 48),
              SizedBox(height: 16),
              Text(
                localizations.noTransactionsYet,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                localizations.transactionsWillAppearHere,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.only(
        bottomLeft: Radius.circular(16),
        bottomRight: Radius.circular(16),
      ),
      child: ListView.separated(
        physics: NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _transactions.length,
        separatorBuilder:
            (context, index) =>
                Divider(height: 1, thickness: 1, indent: 16, endIndent: 16),
        itemBuilder: (context, index) {
          final transaction = _transactions[index];
          return _buildImprovedTransactionItem(transaction);
        },
      ),
    );
  }

  Widget _buildImprovedTransactionItem(Transaction transaction) {
    final localizations = AppLocalizations.of(context)!;

    // Format date
    final DateTime createdAt = DateTime.parse(transaction.createdAt);
    final String formattedDate = DateFormat.yMMMd().format(createdAt);

    // Format price
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN', // Consider using the app's locale
      symbol: '₹',
      decimalDigits: 0,
    );

    // Get status details
    final statusDetails = transaction.getStatusDetails();
    final statusColor = _getStatusColor(statusDetails['color'] as String);

    return InkWell(
      onTap: () => _navigateToTransactionDetail(transaction),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            // Produce image or placeholder with improved styling
            Hero(
              tag: 'transaction_image_${transaction.id}',
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child:
                      transaction.produceImage != null
                          ? Image.network(
                            transaction.produceImage!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[300],
                                child: Icon(
                                  Icons.image,
                                  color: Colors.grey[500],
                                ),
                              );
                            },
                          )
                          : Container(
                            color: Colors.grey[300],
                            child: Icon(Icons.crop, color: Colors.grey[500]),
                          ),
                ),
              ),
            ),

            SizedBox(width: 12),

            // Transaction details with improved layout
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Crop name with better typography
                  Text(
                    transaction.cropName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4),

                  // Improved transaction info with icons
                  Row(
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${transaction.quantity} ${transaction.unitType}',
                        style: TextStyle(fontSize: 13, color: Colors.grey[700]),
                      ),
                      Text(' • ', style: TextStyle(color: Colors.grey[500])),
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        formattedDate,
                        style: TextStyle(fontSize: 13, color: Colors.grey[700]),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Transaction amount and status with improved styling
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Improved amount display
                Text(
                  priceFormat.format(transaction.totalAmount),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
                SizedBox(height: 6),

                // Improved status indicator
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getLocalizedStatus(
                      statusDetails['label'] as String,
                      localizations,
                    ),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getLocalizedStatus(String status, AppLocalizations localizations) {
    // Map English status to localized status
    switch (status.toLowerCase()) {
      case 'accepted':
        return localizations.statusAccepted;
      case 'in progress':
        return localizations.statusInProgress;
      case 'delivered':
        return localizations.statusDelivered;
      case 'closed':
        return localizations.statusClosed;
      case 'cancelled':
        return localizations.statusCancelled;
      default:
        return status;
    }
  }

  Color _getStatusColor(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'green':
        return Colors.green[600]!;
      case 'blue':
        return Colors.blue[600]!;
      case 'orange':
        return Colors.orange[700]!;
      case 'red':
        return Colors.red[600]!;
      default:
        return Colors.grey[700]!;
    }
  }
}
