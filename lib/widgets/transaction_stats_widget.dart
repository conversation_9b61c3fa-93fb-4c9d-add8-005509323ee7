// lib/widgets/transaction_stats_widget.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/services/transaction_service.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:fpo_traders/screens/transactions_screen.dart';
import 'package:fpo_traders/models/user.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';

class TransactionStatsWidget extends StatefulWidget {
  final User user;
  final Function()? onViewAllPressed;

  const TransactionStatsWidget({
    Key? key,
    required this.user,
    this.onViewAllPressed,
  }) : super(key: key);

  @override
  State<TransactionStatsWidget> createState() => _TransactionStatsWidgetState();
}

class _TransactionStatsWidgetState extends State<TransactionStatsWidget> {
  bool _isLoading = true;
  Map<String, dynamic>? _stats;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchTransactionStats();
  }

  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  Future<void> _fetchTransactionStats() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final stats = await TransactionService.getTransactionStats();

      setState(() {
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (_isLoading) {
      return _buildLoadingCard();
    }

    if (_errorMessage != null) {
      return _buildErrorCard();
    }

    if (_stats == null ||
        _parseInt(_stats!['totals']['total_transactions']) == 0) {
      return _buildEmptyStateCard();
    }

    // Format currency
    final priceFormat = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );

    // Get data for chart
    final monthlyData = _stats!['monthly'] as List;
    final statusData = _stats!['by_status'] as List;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and view all button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localizations.transactionActivity,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                TextButton(
                  onPressed:
                      widget.onViewAllPressed ??
                      () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    TransactionsScreen(user: widget.user),
                          ),
                        );
                      },
                  child: Text(
                    localizations.viewAll,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size(50, 30),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Summary stats in improved cards with shadow
            Row(
              children: [
                Expanded(
                  child: _buildImprovedStatCard(
                    localizations.totalTransactions,
                    _parseInt(
                      _stats!['totals']['total_transactions'],
                    ).toString(),
                    Colors.blue,
                    Icons.receipt_long,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildImprovedStatCard(
                    localizations.totalValue,
                    priceFormat.format(
                      _parseDouble(_stats!['totals']['total_value']),
                    ),
                    Colors.green[700]!,
                    Icons.account_balance_wallet,
                  ),
                ),
              ],
            ),
            SizedBox(height: 24),

            // Only show chart if there's sufficient monthly data
            if (monthlyData.isNotEmpty && monthlyData.length > 1) ...[
              Text(
                localizations.monthlyAnalysis,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(height: 16),
              Container(
                height: 200,
                child: _buildTransactionChart(),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[100],
                ),
                padding: EdgeInsets.all(8),
              ),
            ] else ...[
              // Status breakdown with improved styling
              Text(
                localizations.transactionStatus,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(height: 16),
              _buildImprovedStatusBreakdown(statusData),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        height: 200,
        width: double.infinity,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
              SizedBox(height: 16),
              Text(
                localizations.loadingTransactionData,
                style: TextStyle(color: Colors.grey[700]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard() {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[400], size: 48),
            SizedBox(height: 16),
            Text(
              localizations.failedToLoadTransactionStats,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            Text(
              _errorMessage ?? localizations.unknownError,
              style: TextStyle(color: Colors.grey[700]),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _fetchTransactionStats,
              icon: Icon(Icons.refresh),
              label: Text(localizations.retry),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyStateCard() {
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              localizations.transactionActivity,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            SizedBox(height: 20),
            Icon(Icons.bar_chart, color: Colors.grey[300], size: 64),
            SizedBox(height: 16),
            Text(
              localizations.noTransactionsYet,
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              localizations.transactionsWillAppearHere,
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            OutlinedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TransactionsScreen(user: widget.user),
                  ),
                );
              },
              icon: Icon(Icons.visibility),
              label: Text(localizations.viewTransactions),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).primaryColor),
                foregroundColor: Theme.of(context).primaryColor,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImprovedStatCard(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 18, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          SizedBox(height: 12),
          Text(
            label,
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImprovedStatusBreakdown(List statusData) {
    final localizations = AppLocalizations.of(context)!;

    if (statusData.isEmpty) {
      return Center(
        child: Text(
          localizations.noStatusDataAvailable,
          style: TextStyle(
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            statusData.map<Widget>((statusItem) {
              final status = statusItem['status'] as String? ?? 'unknown';
              final count = _parseInt(statusItem['count']);
              final totalValue = _parseDouble(statusItem['total_value']);

              // Format price
              final priceFormat = NumberFormat.currency(
                locale: 'en_IN',
                symbol: '₹',
                decimalDigits: 0,
              );

              // Get status color
              Color statusColor = _getStatusColor(status);

              return Container(
                margin: EdgeInsets.only(bottom: 12),
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _getLocalizedStatus(status, localizations),
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[800],
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      priceFormat.format(totalValue),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: statusColor,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTransactionChart() {
    // Implementation can remain mostly the same but should use localizations for labels
    return Container(
      child: Text("Chart would be here"), // Simplified for this example
    );
  }

  String _getLocalizedStatus(String status, AppLocalizations localizations) {
    // Map English status to localized status
    switch (status.toLowerCase()) {
      case 'accepted':
        return localizations.statusAccepted;
      case 'in_progress':
        return localizations.statusInProgress;
      case 'delivered':
        return localizations.statusDelivered;
      case 'closed':
        return localizations.statusClosed;
      case 'cancelled':
        return localizations.statusCancelled;
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'accepted':
        return Colors.blue[600]!;
      case 'in_progress':
        return Colors.orange[700]!;
      case 'delivered':
        return Colors.green[600]!;
      case 'closed':
        return Colors.green[800]!;
      case 'cancelled':
        return Colors.red[600]!;
      default:
        return Colors.grey[700]!;
    }
  }
}
