// lib/widgets/price_history_chart.dart
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

class PriceHistoryChart extends StatelessWidget {
  final List<Map<String, dynamic>> priceHistory;
  final Color lineColor;

  const PriceHistoryChart({
    Key? key,
    required this.priceHistory,
    this.lineColor = Colors.red,
  }) : super(key: key);

  // Helper method to safely parse price values
  double _parsePrice(dynamic price) {
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  @override
  Widget build(BuildContext context) {
    if (priceHistory.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Text('No price history available'),
      );
    }

    // Sort data by date
    final sortedData = List<Map<String, dynamic>>.from(priceHistory);
    sortedData.sort(
      (a, b) => DateTime.parse(
        a['price_date'].toString(),
      ).compareTo(DateTime.parse(b['price_date'].toString())),
    );

    // Find min and max values for y-axis
    double minY = double.infinity;
    double maxY = 0;

    for (var item in sortedData) {
      final price = _parsePrice(item['price_per_unit']);
      if (price < minY) minY = price;
      if (price > maxY) maxY = price;
    }

    // Handle the case where we have no valid prices
    if (minY == double.infinity) minY = 0;

    // Add some padding to min/max values
    minY = minY * 0.9;
    maxY = maxY * 1.1;

    // Ensure there's a minimum range even if all prices are the same
    if (maxY - minY < 1) {
      minY = maxY - 10;
      if (minY < 0) minY = 0;
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(color: Colors.grey[300], strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(color: Colors.grey[300], strokeWidth: 1);
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                // Show dates on x-axis
                if (value.toInt() >= 0 && value.toInt() < sortedData.length) {
                  final date = DateTime.parse(
                    sortedData[value.toInt()]['price_date'].toString(),
                  );
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      DateFormat('MMM d').format(date),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    '₹${value.toInt()}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey[300]!),
        ),
        minX: 0,
        maxX: sortedData.length - 1.0,
        minY: minY,
        maxY: maxY,
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(sortedData.length, (index) {
              return FlSpot(
                index.toDouble(),
                _parsePrice(sortedData[index]['price_per_unit']),
              );
            }),
            isCurved: true,
            color: lineColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: Colors.white,
                  strokeWidth: 2,
                  strokeColor: lineColor,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: lineColor.withOpacity(0.15),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData(
            tooltipBgColor: Colors.black.withOpacity(0.8),
            getTooltipItems: (List<LineBarSpot> touchedSpots) {
              return touchedSpots.map((spot) {
                final date = DateTime.parse(
                  sortedData[spot.x.toInt()]['price_date'].toString(),
                );
                final price = spot.y;
                return LineTooltipItem(
                  '${DateFormat('MMM d, yyyy').format(date)}\n',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    TextSpan(
                      text: '₹$price',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }
}
