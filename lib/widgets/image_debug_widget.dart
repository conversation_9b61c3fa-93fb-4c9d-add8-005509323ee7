// lib/widgets/image_debug_widget.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/utils/image_debug_tool.dart';
import 'package:fpo_traders/l10n/app_localizations.dart';

class NetworkImageWithDebug extends StatelessWidget {
  final String? imageUrl;
  final double height;
  final double width;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const NetworkImageWithDebug({
    Key? key,
    required this.imageUrl,
    this.height = 160,
    this.width = double.infinity,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    if (imageUrl == null || imageUrl!.isEmpty) {
      return Container(
        height: height,
        width: width,
        color: Colors.grey[300],
        child: errorWidget ?? _defaultErrorWidget(context),
      );
    }

    return GestureDetector(
      onLongPress: () {
        // Launch debug tool on long press
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ImageDebugScreen(imageUrl: imageUrl!),
          ),
        );
      },
      child: Container(
        height: height,
        width: width,
        color: Colors.grey[300],
        child: Stack(
          children: [
            if (placeholder != null)
              Positioned.fill(child: placeholder!)
            else
              Positioned.fill(
                child: Center(child: CircularProgressIndicator()),
              ),
            Positioned.fill(
              child: Image.network(
                imageUrl!,
                fit: fit,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(); // Use the placeholder already in stack
                },
                errorBuilder: (context, error, stackTrace) {
                  print('Error loading image: $error for URL: $imageUrl');
                  return errorWidget ??
                      _defaultErrorWidget(context, withLongPress: true);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _defaultErrorWidget(
    BuildContext context, {
    bool withLongPress = false,
  }) {
    final localizations = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image_not_supported, color: Colors.grey[500], size: 48),
          const SizedBox(height: 8),
          if (withLongPress)
            Text(
              localizations.longPressToDebug,
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            )
          else
            Text(
              localizations.imageUnavailable,
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
        ],
      ),
    );
  }
}
