import 'package:flutter/services.dart';

class NativeMessagingBridge {
  static const MethodChannel _channel = MethodChannel(
    'com.kalso.fpotraders/fcm',
  );

  static Future<String?> getNativeFcmToken() async {
    try {
      final String? token = await _channel.invokeMethod('getFcmToken');
      print("Native bridge FCM token: $token");
      return token;
    } on PlatformException catch (e) {
      print("Error getting native FCM token: ${e.message}");
      return null;
    }
  }
}
