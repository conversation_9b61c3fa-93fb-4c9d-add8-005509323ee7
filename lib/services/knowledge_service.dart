// lib/services/knowledge_service.dart
import 'package:fpo_traders/models/knowledge_article.dart';
import 'package:fpo_traders/services/api_service.dart';

class KnowledgeService {
  // Get list of knowledge articles with pagination
  static Future<Map<String, dynamic>> getArticles({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/knowledge?page=$page&limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        final articlesJson = response['data']['articles'] as List;
        final articles =
            articlesJson
                .map((articleJson) => KnowledgeArticle.fromJson(articleJson))
                .toList();

        return {
          'articles': articles,
          'pagination': response['data']['pagination'],
        };
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch articles');
      }
    } catch (e) {
      throw Exception('Error fetching articles: $e');
    }
  }

  // Get single article details
  static Future<KnowledgeArticle> getArticleById(int articleId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/knowledge/$articleId',
        'GET',
      );

      if (response['status'] == 'success') {
        return KnowledgeArticle.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch article');
      }
    } catch (e) {
      throw Exception('Error fetching article: $e');
    }
  }

  // Toggle like status on article
  static Future<bool> toggleLike(int articleId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/knowledge/$articleId/like',
        'POST',
      );

      if (response['status'] == 'success') {
        return response['data']['isLiked'] ?? false;
      } else {
        throw Exception(response['message'] ?? 'Failed to toggle like');
      }
    } catch (e) {
      throw Exception('Error toggling like: $e');
    }
  }

  // Record article view
  static Future<void> recordView(int articleId) async {
    try {
      await ApiService.authenticatedRequest(
        '/mobile/knowledge/$articleId/view',
        'POST',
      );
    } catch (e) {
      // Silently fail on view recording as it's not critical
      print('Error recording view: $e');
    }
  }
}
