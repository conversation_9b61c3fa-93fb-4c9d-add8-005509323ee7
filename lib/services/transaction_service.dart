// lib/services/transaction_service.dart
import 'package:fpo_traders/models/transaction.dart';
import 'package:fpo_traders/services/api_service.dart';

class TransactionService {
  // Get all transactions with pagination
  // In transaction_service.dart
  static Future<Map<String, dynamic>> getTransactions({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions?page=$page&limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        // Parse the JSON data before creating Transaction objects
        final transactionJsonList = response['data'] as List;

        // Make sure numeric values are properly parsed
        final transactions =
            transactionJsonList.map((json) {
              // Ensure numeric values are properly parsed
              if (json['price_per_unit'] is String) {
                json['price_per_unit'] =
                    double.tryParse(json['price_per_unit']) ?? 0.0;
              }
              if (json['total_amount'] is String) {
                json['total_amount'] =
                    double.tryParse(json['total_amount']) ?? 0.0;
              }
              if (json['quantity'] is String) {
                json['quantity'] = double.tryParse(json['quantity']) ?? 0.0;
              }

              return Transaction.fromJson(json);
            }).toList();

        return {
          'transactions': transactions,
          'pagination': response['pagination'],
        };
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch transactions');
      }
    } catch (e) {
      throw Exception('Error fetching transactions: $e');
    }
  }

  // Get transactions by type
  static Future<Map<String, dynamic>> getTransactionsByType({
    required String type,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions/by-type/$type?page=$page&limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        final transactionJsonList = response['data'] as List;
        final transactions =
            transactionJsonList
                .map((json) => Transaction.fromJson(json))
                .toList();

        return {
          'transactions': transactions,
          'pagination': response['pagination'],
        };
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch transactions');
      }
    } catch (e) {
      throw Exception('Error fetching transactions by type: $e');
    }
  }

  // Get recent transactions
  static Future<List<Transaction>> getRecentTransactions({
    int limit = 5,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions/recent?limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        final transactionJsonList = response['data'] as List;
        return transactionJsonList
            .map((json) => Transaction.fromJson(json))
            .toList();
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch recent transactions',
        );
      }
    } catch (e) {
      throw Exception('Error fetching recent transactions: $e');
    }
  }

  // Get transaction statistics
  static Future<Map<String, dynamic>> getTransactionStats() async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions/stats',
        'GET',
      );

      if (response['status'] == 'success') {
        return response['data'];
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch transaction statistics',
        );
      }
    } catch (e) {
      throw Exception('Error fetching transaction statistics: $e');
    }
  }

  // Get transaction details
  static Future<Transaction> getTransactionDetails(int transactionId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions/$transactionId',
        'GET',
      );

      if (response['status'] == 'success') {
        return Transaction.fromJson(response['data']);
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch transaction details',
        );
      }
    } catch (e) {
      throw Exception('Error fetching transaction details: $e');
    }
  }

  // Update transaction status
  static Future<bool> updateTransactionStatus(
    int transactionId,
    String status,
  ) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/transactions/$transactionId/status',
        'PUT', // Using PUT as the primary method
        body: {'status': status},
      );

      return response['status'] == 'success';
    } catch (e) {
      // If PUT fails, try POST as a fallback
      try {
        final response = await ApiService.authenticatedRequest(
          '/mobile/transactions/$transactionId/status',
          'POST',
          body: {'status': status},
        );
        return response['status'] == 'success';
      } catch (postError) {
        // Finally try PATCH as a last resort
        try {
          final response = await ApiService.authenticatedRequest(
            '/mobile/transactions/$transactionId/status',
            'PATCH',
            body: {'status': status},
          );
          return response['status'] == 'success';
        } catch (patchError) {
          throw Exception(
            'Failed to update transaction status: Unable to find a supported HTTP method',
          );
        }
      }
    }
  }
}
