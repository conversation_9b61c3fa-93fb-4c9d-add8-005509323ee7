// lib/services/requirement_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:fpo_traders/models/requirement.dart';
import 'package:fpo_traders/services/api_service.dart';

class RequirementService {
  // Get requirements with optional filtering
  static Future<Map<String, dynamic>> getRequirements({
    int page = 1,
    int limit = 10,
    String type = 'active', // 'active', 'for-my-crops', or 'matching'
  }) async {
    final token = await ApiService.getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    String endpoint;
    switch (type) {
      case 'for-my-crops':
        endpoint = '/mobile/requirements/for-my-crops';
        break;
      case 'matching':
        endpoint = '/mobile/requirements/matching';
        break;
      case 'active':
      default:
        endpoint = '/mobile/requirements/active';
        break;
    }

    // Add pagination parameters
    endpoint += '?page=$page&limit=$limit';

    try {
      final response = await http.get(
        Uri.parse('${ApiService.baseUrl}$endpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['status'] == 'success') {
        // Return the raw data for flexibility
        return responseData;
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to fetch requirements',
        );
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get requirement details by ID
  static Future<Requirement> getRequirementDetails(int requirementId) async {
    final token = await ApiService.getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    final endpoint = '/mobile/requirements/$requirementId';

    try {
      final response = await http.get(
        Uri.parse('${ApiService.baseUrl}$endpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['status'] == 'success') {
        return Requirement.fromJson(responseData['data']);
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to fetch requirement details',
        );
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Express interest in a requirement with existing produce
  static Future<Map<String, dynamic>> expressInterest({
    required int requirementId,
    required int produceId,
    required double quantity,
    String? notes,
  }) async {
    final token = await ApiService.getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    final endpoint = '/mobile/requirements/$requirementId/express-interest';

    try {
      final response = await http.post(
        Uri.parse('${ApiService.baseUrl}$endpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'produceId': produceId,
          'quantity': quantity,
          'notes': notes,
        }),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 201 && responseData['status'] == 'success') {
        return responseData;
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to express interest',
        );
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Create new produce specifically for a requirement
  static Future<Map<String, dynamic>> createProduceForRequirement({
    required int requirementId,
    required Map<String, dynamic> produceData,
    required List<http.MultipartFile> images,
  }) async {
    final token = await ApiService.getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    final endpoint = '/mobile/requirements/$requirementId/create-produce';

    try {
      // Create a multipart request
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiService.baseUrl}$endpoint'),
      );

      // Add the authorization header
      request.headers['Authorization'] = 'Bearer $token';

      // Add all text fields
      produceData.forEach((key, value) {
        if (value != null) {
          request.fields[key] = value.toString();
        }
      });

      // Add images if provided
      for (var i = 0; i < images.length; i++) {
        if (i == 0) {
          request.files.add(
            http.MultipartFile(
              'primary_image',
              images[i].finalize(),
              images[i].length,
              filename: images[i].filename,
            ),
          );
        } else if (i == 1) {
          request.files.add(
            http.MultipartFile(
              'secondary_image',
              images[i].finalize(),
              images[i].length,
              filename: images[i].filename,
            ),
          );
        } else if (i == 2) {
          request.files.add(
            http.MultipartFile(
              'tertiary_image',
              images[i].finalize(),
              images[i].length,
              filename: images[i].filename,
            ),
          );
        }
      }

      // Send the request
      final streamedResponse = await request.send();

      // Convert the streamed response to a regular response
      final response = await http.Response.fromStream(streamedResponse);

      final responseData = json.decode(response.body);

      if (response.statusCode == 201 && responseData['status'] == 'success') {
        return responseData;
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to create produce for requirement',
        );
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get requirement transactions for the farmer
  static Future<Map<String, dynamic>> getRequirementTransactions({
    int page = 1,
    int limit = 10,
  }) async {
    final token = await ApiService.getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    final endpoint =
        '/mobile/requirements/my-transactions?page=$page&limit=$limit';

    try {
      final response = await http.get(
        Uri.parse('${ApiService.baseUrl}$endpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['status'] == 'success') {
        return responseData;
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to fetch requirement transactions',
        );
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}
