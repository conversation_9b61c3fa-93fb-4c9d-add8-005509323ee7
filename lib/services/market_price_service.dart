// lib/services/market_price_service.dart
import 'package:fpo_traders/models/market_price.dart';
import 'package:fpo_traders/services/api_service.dart';

class MarketPriceService {
  // Get latest market prices with pagination
  static Future<Map<String, dynamic>> getLatestPrices({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/prices/latest?page=$page&limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        // The data is directly an array, not nested in a data field
        final pricesJson = response['data'] as List;

        // Convert each item with error handling
        final prices = <MarketPrice>[];
        for (var i = 0; i < pricesJson.length; i++) {
          try {
            prices.add(MarketPrice.fromJson(pricesJson[i]));
          } catch (e) {
            print('Error parsing item $i: $e');
            print('Item data: ${pricesJson[i]}');
            // Continue with other items instead of failing everything
          }
        }

        // Since pagination might not be in the expected structure either,
        // we'll create a default one
        final pagination = {
          'total': pricesJson.length,
          'totalPages': 1,
          'currentPage': page,
          'limit': limit,
        };

        return {
          'prices': prices,
          'pagination': response['pagination'] ?? pagination,
        };
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch market prices');
      }
    } catch (e) {
      print('Error in getLatestPrices: $e');
      throw Exception('Error fetching market prices: $e');
    }
  }

  // Get price history for a specific product
  static Future<List<Map<String, dynamic>>> getPriceHistory(
    int productId, {
    int period = 30,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/prices/history/$productId?period=$period',
        'GET',
      );

      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch price history');
      }
    } catch (e) {
      throw Exception('Error fetching price history: $e');
    }
  }
}
