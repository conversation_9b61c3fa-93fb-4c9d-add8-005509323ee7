// lib/services/announcement_service.dart
import 'package:fpo_traders/models/announcement.dart';
import 'package:fpo_traders/services/api_service.dart';

class AnnouncementService {
  // Get list of announcements with pagination
  static Future<Map<String, dynamic>> getAnnouncements({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/announcements?page=$page&limit=$limit',
        'GET',
      );

      if (response['status'] == 'success') {
        final announcementsJson = response['data']['announcements'] as List;
        final announcements =
            announcementsJson
                .map((json) => Announcement.fromJson(json))
                .toList();

        return {
          'announcements': announcements,
          'pagination': response['data']['pagination'],
        };
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch announcements');
      }
    } catch (e) {
      throw Exception('Error fetching announcements: $e');
    }
  }

  // Get single announcement details
  static Future<Announcement> getAnnouncementById(int announcementId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/announcements/$announcementId',
        'GET',
      );

      if (response['status'] == 'success') {
        return Announcement.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch announcement');
      }
    } catch (e) {
      throw Exception('Error fetching announcement: $e');
    }
  }
}
