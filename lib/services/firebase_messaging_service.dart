// lib/services/firebase_messaging_service.dart
import 'dart:async';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'package:fpo_traders/native_messaging_bridge.dart';

// Background message handler - Keep this outside the class
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Don't initialize Firebase again - it slows down background handling
  // Only initialize if it's not already initialized
  if (Firebase.apps.isEmpty) {
    await Firebase.initializeApp();
  }

  print("Handling background message: ${message.messageId}");
  print("Message data: ${message.data}");
  print("Message notification: ${message.notification?.title}");
}

class FirebaseMessagingService {
  static FirebaseMessaging? _messaging;
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static String? _fcmToken;

  // Flag to avoid duplicate initializations
  static bool _isInitializing = false;
  static bool _isInitialized = false;

  // Completer to track initialization state
  static final Completer<bool> _initializationCompleter = Completer<bool>();

  // Initialize the Firebase Messaging service
  static Future<bool> initialize() async {
    // Prevent multiple simultaneous initializations
    if (_isInitializing) {
      return _initializationCompleter.future;
    }

    // If already initialized, return immediately
    if (_isInitialized) {
      return true;
    }

    _isInitializing = true;

    try {
      // Get messaging instance - we assume Firebase.initializeApp() was already called in main.dart
      _messaging = FirebaseMessaging.instance;

      // Set up message handlers - do this first for better notification handling
      _setupNotificationHandlers();

      // Only for iOS, request permissions in parallel with other operations
      if (Platform.isIOS) {
        _requestIOSPermissionsAsync();
      }

      // Initialize notifications in parallel - don't await
      _initializeLocalNotificationsAsync();

      // Don't await token retrieval - do it in background
      _scheduleTokenRetrievalsAsync();

      // Mark as initialized and return
      _isInitialized = true;
      _isInitializing = false;
      _initializationCompleter.complete(true);

      print('Firebase Messaging initialization started successfully');
      return true;
    } catch (e, stackTrace) {
      print('Error initializing Firebase Messaging: $e');
      print('Stack trace: $stackTrace');

      _isInitializing = false;
      _initializationCompleter.complete(false);
      return false;
    }
  }

  static void _setupNotificationHandlers() {
    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');

      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
        _showLocalNotification(message);
      }
    });

    // Handle app open from notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('A new onMessageOpenedApp event was published!');
      _handleMessageOpenedApp(message);
    });

    // Check for initial message (don't await)
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        print(
          'App opened from terminated state with message: ${message.messageId}',
        );
        _handleInitialMessage(message);
      }
    });
  }

  // Initialize notifications asynchronously without blocking
  static Future<void> _initializeLocalNotificationsAsync() async {
    try {
      _localNotifications = FlutterLocalNotificationsPlugin();

      // Platform-specific settings
      final InitializationSettings initializationSettings;

      // iOS-specific settings
      if (Platform.isIOS) {
        final DarwinInitializationSettings iosSettings =
            DarwinInitializationSettings(
              requestSoundPermission: true,
              requestBadgePermission: true,
              requestAlertPermission: true,
            );

        initializationSettings = InitializationSettings(iOS: iosSettings);

        // Set foreground notification options
        await FirebaseMessaging.instance
            .setForegroundNotificationPresentationOptions(
              alert: true,
              badge: true,
              sound: true,
            );
      }
      // Android-specific settings (add if needed)
      else {
        initializationSettings = const InitializationSettings();
      }

      // Initialize local notifications
      await _localNotifications?.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          print('Notification tapped with payload: ${response.payload}');
          _handleNotificationTap(response);
        },
      );

      print('Local notifications initialized successfully');
    } catch (e) {
      print('Error initializing local notifications: $e');
    }
  }

  // Request iOS permissions without blocking
  // Update the iOS permissions handling
  static Future<void> _requestIOSPermissionsAsync() async {
    try {
      print("Starting iOS permission request sequence...");

      // Force a longer delay before requesting permissions to ensure UI is ready
      await Future.delayed(const Duration(seconds: 2));

      print("Requesting permissions dialog to appear...");
      final NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      // Wait briefly after the permission dialog is handled by user
      await Future.delayed(const Duration(milliseconds: 500));

      // Get the LATEST status (after user interaction)
      final NotificationSettings latestSettings =
          await _messaging!.getNotificationSettings();

      print('FINAL permission status: ${latestSettings.authorizationStatus}');

      // Check if still not determined after user interaction
      if (latestSettings.authorizationStatus ==
          AuthorizationStatus.notDetermined) {
        print(
          "⚠️ Warning: Permission still shows as notDetermined even after dialog",
        );

        // Register token anyway - some devices may report status incorrectly
        _attemptTokenRetrieval();
      }
    } catch (e) {
      print('Error in iOS permission flow: $e');
    }
  }

  // Schedule token retrievals in background
  static Future<void> _scheduleTokenRetrievalsAsync() async {
    // First attempt
    _attemptTokenRetrieval();

    // Schedule more attempts with delays
    Timer(const Duration(seconds: 2), () {
      _attemptTokenRetrieval();

      // Schedule one more attempt
      Timer(const Duration(seconds: 3), _attemptTokenRetrieval);
    });
  }

  static Future<void> _attemptTokenRetrieval() async {
    try {
      print("Attempting to get FCM token...");

      // Try to get token even if permissions appear not determined
      _fcmToken = await _messaging?.getToken();

      if (_fcmToken != null && _fcmToken!.isNotEmpty) {
        print("Successfully retrieved FCM token: $_fcmToken");

        // Report that we have a token regardless of permission status
        sendTokenToServer(_fcmToken);

        // Set up token refresh
        _messaging?.onTokenRefresh.listen((newToken) {
          print("FCM Token refreshed: $newToken");
          _fcmToken = newToken;
          sendTokenToServer(newToken);
        });
      } else {
        // Try native bridge as fallback
        print("Trying native bridge...");
        _fcmToken = await NativeMessagingBridge.getNativeFcmToken();
        if (_fcmToken != null) {
          print("Got token through native bridge: $_fcmToken");
          sendTokenToServer(_fcmToken);
        }
      }
    } catch (e) {
      print("Error in token retrieval attempt: $e");
    }
  }

  // Get the FCM token - public method for external access
  static Future<String?> getToken() async {
    // If we already have a token, return it
    if (_fcmToken != null && _fcmToken!.isNotEmpty) {
      return _fcmToken;
    }

    try {
      // Try to get the token directly
      _fcmToken = await _messaging?.getToken();
      print("Flutter FCM Token: $_fcmToken");

      // If null, try native bridge
      if (_fcmToken == null || _fcmToken!.isEmpty) {
        print("Flutter FCM token is null, trying native bridge");
        _fcmToken = await NativeMessagingBridge.getNativeFcmToken();
        print("Got FCM token from native bridge: $_fcmToken");
      }

      return _fcmToken;
    } catch (e) {
      print("Error getting FCM token: $e");
      return null;
    }
  }

  // Send FCM token to server without blocking
  static Future<void> sendTokenToServer(String? token) async {
    if (token == null || token.isEmpty) return;

    try {
      print('Sending FCM token to server: $token');
      final success = await ApiService.registerFCMToken(token);
      print('FCM token registered with server: $success');
    } catch (e) {
      print('Failed to register FCM token with server: $e');
      // Don't throw - just log the error
    }
  }

  // Remove FCM token from server when logging out
  static Future<void> removeTokenFromServer() async {
    if (_fcmToken == null || _fcmToken!.isEmpty) return;

    try {
      final success = await ApiService.unregisterFCMToken(_fcmToken!);
      print('Token removed from server: $success');
    } catch (e) {
      print('Failed to remove token from server: $e');
    }
  }

  // Show a local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    if (_localNotifications == null) {
      print('Local notifications not initialized yet');
      return;
    }

    RemoteNotification? notification = message.notification;

    if (notification != null) {
      if (Platform.isIOS) {
        // iOS notification
        _localNotifications!.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          payload: message.data['route'],
        );
      }
      // Add Android notification handling if needed
    }
  }

  // Handle notification tap
  static void _handleNotificationTap(NotificationResponse response) {
    // Extract payload data
    final String? payload = response.payload;
    if (payload != null && payload.isNotEmpty) {
      // You would typically use a NavigationService or similar to navigate
      print('Should navigate to: $payload');
    }
  }

  // Handle when a notification opens the app
  static void _handleMessageOpenedApp(RemoteMessage message) {
    // Navigate based on message data
    final String? route = message.data['route'];
    if (route != null && route.isNotEmpty) {
      // Navigate to the specified route
      print('Should navigate to: $route');
    }
  }

  // Handle initial message when app is launched from terminated state
  static void _handleInitialMessage(RemoteMessage message) {
    // Navigate based on the message
    final String? route = message.data['route'];
    if (route != null && route.isNotEmpty) {
      // The navigation should be done after the app is fully initialized
      print('Should navigate to: $route after startup');
    }
  }

  // Subscribe to a topic
  static Future<void> subscribeToTopic(String topic) async {
    await _messaging?.subscribeToTopic(topic);
    print('Subscribed to topic: $topic');
  }

  // Unsubscribe from a topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    await _messaging?.unsubscribeFromTopic(topic);
    print('Unsubscribed from topic: $topic');
  }

  // Clean up resources and unregister token when logging out
  static Future<void> cleanUp() async {
    await removeTokenFromServer();
    _fcmToken = null;
  }
}
