// lib/services/farmer_produce_service.dart
import 'package:fpo_traders/models/farmer_produce.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'dart:convert';
import 'package:http_parser/http_parser.dart';

class FarmerProduceService {
  // Get farmer's produce listings
  static Future<Map<String, dynamic>> getMyProduce({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      String endpoint = '/mobile/produce/my-produce?page=$page&limit=$limit';

      // Add status filter if provided
      if (status != null && status != 'all') {
        endpoint += '&status=$status';
      }

      final response = await ApiService.authenticatedRequest(endpoint, 'GET');

      if (response['status'] == 'success') {
        final produceJson = response['data'];
        final List<FarmerProduce> produce = [];

        if (produceJson is List) {
          for (var item in produceJson) {
            produce.add(FarmerProduce.fromJson(item));
          }
        }

        final pagination =
            response['pagination'] ??
            {
              'total': produce.length,
              'totalPages': 1,
              'currentPage': page,
              'limit': limit,
            };

        return {'produce': produce, 'pagination': pagination};
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch produce listings',
        );
      }
    } catch (e) {
      throw Exception('Error fetching produce listings: $e');
    }
  }

  // Get single produce details
  static Future<FarmerProduce> getProduceDetails(int produceId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/produce/my-produce/$produceId',
        'GET',
      );

      if (response['status'] == 'success') {
        return FarmerProduce.fromJson(response['data']);
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch produce details',
        );
      }
    } catch (e) {
      throw Exception('Error fetching produce details: $e');
    }
  }

  // Create new produce listing
  static Future<int> createProduce(
    Map<String, dynamic> produceData, {
    File? primaryImage,
    File? secondaryImage,
    File? tertiaryImage,
  }) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final uri = Uri.parse('${ApiService.baseUrl}/mobile/produce');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      request.headers['Authorization'] = 'Bearer $token';

      // Add produce data fields
      request.fields['product_id'] = produceData['product_id'].toString();
      request.fields['quantity'] = produceData['quantity'].toString();
      request.fields['unit_type'] = produceData['unit_type'];
      request.fields['available_from'] = produceData['available_from'];

      // Add optional fields if present
      if (produceData['variety'] != null)
        request.fields['variety'] = produceData['variety'];
      if (produceData['description'] != null)
        request.fields['description'] = produceData['description'];
      if (produceData['harvest_date'] != null)
        request.fields['harvest_date'] = produceData['harvest_date'];
      if (produceData['available_until'] != null)
        request.fields['available_until'] = produceData['available_until'];

      // Add images if provided
      if (primaryImage != null) {
        final primaryImageStream = http.ByteStream(primaryImage.openRead());
        final primaryImageLength = await primaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(primaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final primaryImageMultipart = http.MultipartFile(
          'primary_image',
          primaryImageStream,
          primaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(primaryImageMultipart);
      }

      if (secondaryImage != null) {
        final secondaryImageStream = http.ByteStream(secondaryImage.openRead());
        final secondaryImageLength = await secondaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(secondaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final secondaryImageMultipart = http.MultipartFile(
          'secondary_image',
          secondaryImageStream,
          secondaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch + 1}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(secondaryImageMultipart);
      }

      if (tertiaryImage != null) {
        final tertiaryImageStream = http.ByteStream(tertiaryImage.openRead());
        final tertiaryImageLength = await tertiaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(tertiaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final tertiaryImageMultipart = http.MultipartFile(
          'tertiary_image',
          tertiaryImageStream,
          tertiaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch + 2}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(tertiaryImageMultipart);
      }

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Log response for debugging
      print('Create produce response: ${response.statusCode} ${response.body}');

      // Parse response
      final responseData = json.decode(response.body);

      if (responseData['status'] == 'success') {
        return responseData['data']['id'];
      } else {
        throw Exception(
          responseData['message'] ?? 'Failed to create produce listing',
        );
      }
    } catch (e) {
      print('Exception during create produce: $e');
      throw Exception('Error creating produce listing: $e');
    }
  }

  // Update produce listing
  static Future<bool> updateProduce(
    int produceId,
    Map<String, dynamic> produceData, {
    File? primaryImage,
    File? secondaryImage,
    File? tertiaryImage,
  }) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final uri = Uri.parse('${ApiService.baseUrl}/mobile/produce/$produceId');
      final request = http.MultipartRequest('PUT', uri);

      // Add headers
      request.headers['Authorization'] = 'Bearer $token';

      // Add produce data fields
      request.fields['product_id'] = produceData['product_id'].toString();
      request.fields['quantity'] = produceData['quantity'].toString();
      request.fields['unit_type'] = produceData['unit_type'];
      request.fields['available_from'] = produceData['available_from'];

      // Add optional fields if present
      if (produceData['variety'] != null)
        request.fields['variety'] = produceData['variety'];
      if (produceData['description'] != null)
        request.fields['description'] = produceData['description'];
      if (produceData['harvest_date'] != null)
        request.fields['harvest_date'] = produceData['harvest_date'];
      if (produceData['available_until'] != null)
        request.fields['available_until'] = produceData['available_until'];

      // Add images if provided
      if (primaryImage != null) {
        final primaryImageStream = http.ByteStream(primaryImage.openRead());
        final primaryImageLength = await primaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(primaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final primaryImageMultipart = http.MultipartFile(
          'primary_image',
          primaryImageStream,
          primaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(primaryImageMultipart);
      }

      if (secondaryImage != null) {
        final secondaryImageStream = http.ByteStream(secondaryImage.openRead());
        final secondaryImageLength = await secondaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(secondaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final secondaryImageMultipart = http.MultipartFile(
          'secondary_image',
          secondaryImageStream,
          secondaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch + 1}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(secondaryImageMultipart);
      }

      if (tertiaryImage != null) {
        final tertiaryImageStream = http.ByteStream(tertiaryImage.openRead());
        final tertiaryImageLength = await tertiaryImage.length();

        // Determine file extension and content type
        final fileExtension = path.extension(tertiaryImage.path).toLowerCase();
        var contentType = 'image/jpeg'; // Default

        if (fileExtension == '.png') {
          contentType = 'image/png';
        } else if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
          contentType = 'image/jpeg';
        }

        final tertiaryImageMultipart = http.MultipartFile(
          'tertiary_image',
          tertiaryImageStream,
          tertiaryImageLength,
          filename:
              'image${DateTime.now().millisecondsSinceEpoch + 2}.jpg', // Enforce jpg extension
          contentType: MediaType.parse(contentType),
        );
        request.files.add(tertiaryImageMultipart);
      }

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Log response for debugging
      print('Update produce response: ${response.statusCode} ${response.body}');

      // Parse response
      final responseData = json.decode(response.body);

      return responseData['status'] == 'success';
    } catch (e) {
      print('Exception during update produce: $e');
      throw Exception('Error updating produce listing: $e');
    }
  }

  // Delete produce listing
  static Future<bool> deleteProduce(int produceId) async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/produce/$produceId',
        'DELETE',
      );

      return response['status'] == 'success';
    } catch (e) {
      throw Exception('Error deleting produce listing: $e');
    }
  }

  static Future<bool> directUpdateProduceStatus(
    int produceId,
    String status,
  ) async {
    try {
      // Get the token
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Base URL
      final baseUrl = ApiService.baseUrl;
      final url = Uri.parse('$baseUrl/mobile/produce/$produceId/status');

      print('Attempting direct PUT request to: $url');

      // Make a direct PUT request
      final http.Response response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'status': status}),
      );

      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = jsonDecode(response.body);
        return responseData['status'] == 'success';
      } else {
        throw Exception('HTTP error ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('Direct PUT failed: $e');
      throw Exception('Failed to directly update status: $e');
    }
  }

  // Update produce status - tries multiple HTTP methods if needed
  static Future<bool> updateProduceStatus(int produceId, String status) async {
    // For debugging
    print('Attempting to update produce $produceId status to $status');

    // Try PUT first (most widely supported method for updates)
    try {
      print('Trying PUT method...');
      final response = await ApiService.authenticatedRequest(
        '/mobile/produce/$produceId/status',
        'PUT',
        body: {'status': status},
      );
      print('PUT succeeded: ${response['status']}');
      return response['status'] == 'success';
    } catch (e) {
      print('PUT failed: $e');

      // If PUT fails, try POST as a fallback
      try {
        print('Trying POST method...');
        final response = await ApiService.authenticatedRequest(
          '/mobile/produce/$produceId/status',
          'POST',
          body: {'status': status},
        );
        print('POST succeeded: ${response['status']}');
        return response['status'] == 'success';
      } catch (postError) {
        print('POST also failed: $postError');

        // Final attempt with PATCH, just in case
        try {
          print('Trying PATCH method as last resort...');
          final response = await ApiService.authenticatedRequest(
            '/mobile/produce/$produceId/status',
            'PATCH',
            body: {'status': status},
          );
          print('PATCH succeeded: ${response['status']}');
          return response['status'] == 'success';
        } catch (patchError) {
          print('All methods failed. PATCH error: $patchError');
          throw Exception(
            'Failed to update produce status: Unable to find a supported HTTP method',
          );
        }
      }
    }
  }

  // Get available crops for creating produce listings
  static Future<List<Map<String, dynamic>>> getAvailableCrops() async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/produce/available-crops',
        'GET',
      );

      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw Exception(
          response['message'] ?? 'Failed to fetch available crops',
        );
      }
    } catch (e) {
      // If there's an API error or the endpoint isn't implemented yet,
      // return some fallback data
      print('Error fetching available crops: $e');
      return [
        {'id': 1, 'crop_name': 'Rice'},
        {'id': 2, 'crop_name': 'Wheat'},
        {'id': 3, 'crop_name': 'Corn'},
        {'id': 4, 'crop_name': 'Tomato'},
        {'id': 5, 'crop_name': 'Potato'},
        {'id': 6, 'crop_name': 'Onion'},
        {'id': 7, 'crop_name': 'Carrot'},
        {'id': 8, 'crop_name': 'Cucumber'},
      ];
    }
  }

  // Bulk update produce statuses (expired to available)
  static Future<Map<String, dynamic>> bulkUpdateExpiredProduce() async {
    try {
      final response = await ApiService.authenticatedRequest(
        '/mobile/produce/bulk-update-expired',
        'POST',
      );

      if (response['status'] == 'success') {
        return {
          'success': true,
          'updatedCount': response['data']['updatedCount'] ?? 0,
          'message':
              response['message'] ?? 'Successfully updated produce listings',
        };
      } else {
        throw Exception(
          response['message'] ?? 'Failed to update expired produce',
        );
      }
    } catch (e) {
      throw Exception('Error updating expired produce: $e');
    }
  }
}
