// lib/services/api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io' show Platform;

class ApiService {
  // Use stored URL if available, otherwise fall back to default
  static String? _storedBaseUrl;

  static String get baseUrl {
    // Check if we have a stored/configured URL
    if (_storedBaseUrl != null) {
      return _storedBaseUrl!;
    }

    // Otherwise use default logic
    // const String localIp = '************';
    // const String localIp = 'localhost';
    const String localIp = '*************';

    if (Platform.isAndroid) {
      // Android emulator
      return 'http://********:3000/api';
    } else if (Platform.isIOS) {
      // iOS device or simulator
      bool isSimulator =
          !Platform.environment.containsKey('SIMULATOR_DEVICE_NAME');

      // If not simulator, assume physical device
      return isSimulator
          ? 'http://$localIp:3000/api'
          : 'http://$localIp:3000/api';
    }

    return 'http://$localIp:3000/api'; // fallback for other platforms
  }

  // Allow setting a custom base URL (useful for dynamic environments)
  static Future<void> setBaseUrl(String url) async {
    _storedBaseUrl = url;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('api_base_url', url);
  }

  // Load base URL from preferences on app start
  static Future<void> loadBaseUrl() async {
    final prefs = await SharedPreferences.getInstance();
    _storedBaseUrl = prefs.getString('api_base_url');
  }

  // Get base URL without the /api path - for use with image URLs
  static String get baseUrlWithoutPath {
    final fullUrl = baseUrl;
    final apiIndex = fullUrl.lastIndexOf('/api');
    if (apiIndex != -1) {
      return fullUrl.substring(0, apiIndex);
    }
    return fullUrl;
  }

  static Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  static Map<String, dynamic> parseResponse(http.Response response) {
    try {
      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Failed to parse response: ${e.toString()}',
      };
    }
  }

  // For storing the auth token
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Login method
  static Future<Map<String, dynamic>> login(
    String username,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/mobile/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'username': username, 'password': password}),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['status'] == 'success') {
        // Save token for future authenticated requests
        await saveToken(responseData['data']['token']);
        return responseData;
      } else {
        // Handle error response
        throw Exception(responseData['message'] ?? 'Login failed');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Method for authenticated requests
  static Future<dynamic> authenticatedRequest(
    String endpoint,
    String method, {
    Map<String, dynamic>? body,
  }) async {
    final token = await getToken();
    if (token == null) {
      throw Exception('Not authenticated');
    }

    try {
      late http.Response response;
      final Uri uri = Uri.parse('$baseUrl$endpoint');

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: headers);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: headers,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: headers,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: headers);
          break;
        default:
          throw Exception('Unsupported method');
      }

      final responseData = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return responseData;
      } else {
        throw Exception(responseData['message'] ?? 'Request failed');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Method to register FCM token with the server
  static Future<bool> registerFCMToken(String token) async {
    try {
      // Get the auth token
      final authToken = await getToken();
      if (authToken == null) {
        print('No auth token found for FCM registration');
        return false;
      }

      // Get device info
      final String deviceType =
          Platform.isAndroid ? 'android' : (Platform.isIOS ? 'ios' : 'unknown');
      final String deviceName =
          Platform.isAndroid
              ? 'Android Device'
              : (Platform.isIOS ? 'iOS Device' : 'Unknown Device');

      // Make the API request
      final response = await http.post(
        Uri.parse('$baseUrl/mobile/notifications/register-token'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: json.encode({
          'token': token,
          'device_type': deviceType,
          'device_name': deviceName,
        }),
      );

      // Parse and validate response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        return responseData['status'] == 'success';
      } else {
        print(
          'FCM registration failed with status code: ${response.statusCode}',
        );
        return false;
      }
    } catch (e) {
      print('Error registering FCM token: $e');
      return false;
    }
  }

  // Method to unregister FCM token when logging out
  static Future<bool> unregisterFCMToken(String token) async {
    try {
      final authToken = await getToken();
      if (authToken == null) {
        print('No auth token found for FCM unregistration');
        return false;
      }

      // Make the API request to remove the token
      // Note: Your API may have a different endpoint or method for this
      final response = await http.post(
        Uri.parse('$baseUrl/mobile/notifications/unregister-token'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: json.encode({'token': token}),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        return responseData['status'] == 'success';
      } else {
        return false;
      }
    } catch (e) {
      print('Error unregistering FCM token: $e');
      return false;
    }
  }
}
