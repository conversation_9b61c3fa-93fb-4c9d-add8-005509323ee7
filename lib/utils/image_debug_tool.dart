// lib/utils/image_debug_tool.dart
import 'package:flutter/material.dart';
import 'package:fpo_traders/services/api_service.dart';
import 'dart:async';

class ImageDebugScreen extends StatefulWidget {
  final String imageUrl;

  const ImageDebugScreen({Key? key, required this.imageUrl}) : super(key: key);

  @override
  State<ImageDebugScreen> createState() => _ImageDebugScreenState();
}

class _ImageDebugScreenState extends State<ImageDebugScreen> {
  late String processedUrl;
  bool isLoading = true;
  String error = '';

  @override
  void initState() {
    super.initState();
    processedUrl = _processImageUrl(widget.imageUrl);
    _testImageLoad();
  }

  String _processImageUrl(String url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      final baseUrl =
          url.startsWith('/')
              ? ApiService.baseUrlWithoutPath
              : '${ApiService.baseUrlWithoutPath}/';
      return '$baseUrl$url';
    }
    return url;
  }

  Future<void> _testImageLoad() async {
    setState(() {
      isLoading = true;
      error = '';
    });

    try {
      final networkImage = NetworkImage(processedUrl);
      final imageStream = networkImage.resolve(ImageConfiguration());

      final completer = Completer<void>();
      final listener = ImageStreamListener(
        (ImageInfo image, bool synchronousCall) {
          completer.complete();
        },
        onError: (exception, stackTrace) {
          completer.completeError(exception);
        },
      );

      imageStream.addListener(listener);

      try {
        await completer.future;
        setState(() {
          isLoading = false;
        });
      } catch (e) {
        setState(() {
          isLoading = false;
          error = 'Error loading image: $e';
        });
      } finally {
        imageStream.removeListener(listener);
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        error = 'Error creating image stream: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Image Debug')),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Original URL:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SelectableText(widget.imageUrl),
            SizedBox(height: 16),

            Text(
              'Processed URL:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SelectableText(processedUrl),
            SizedBox(height: 24),

            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child:
                  isLoading
                      ? Center(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: CircularProgressIndicator(),
                        ),
                      )
                      : error.isNotEmpty
                      ? Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(error, style: TextStyle(color: Colors.red)),
                      )
                      : Text(
                        'Image loaded successfully!',
                        style: TextStyle(color: Colors.green),
                      ),
            ),
            SizedBox(height: 24),

            Text(
              'Image Preview:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.network(
                processedUrl,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 40,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Failed to load image',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 24),

            ElevatedButton(
              onPressed: _testImageLoad,
              child: Text('Test Again'),
            ),
          ],
        ),
      ),
    );
  }
}

// Add a Completer import at the top
