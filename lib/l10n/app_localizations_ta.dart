// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tamil (`ta`).
class AppLocalizationsTa extends AppLocalizations {
  AppLocalizationsTa([String locale = 'ta']) : super(locale);

  @override
  String get appTitle => 'FPO TRADERS';

  @override
  String get languageSelectionTitle => 'உங்கள் விருப்பமான மொழியைத் தேர்ந்தெடுக்கவும்';

  @override
  String get english => 'English';

  @override
  String get tamil => 'தமிழ்';

  @override
  String get loginToYourAccount => 'உங்கள் கணக்கில் உள்நுழைக';

  @override
  String get username => 'பயனர்பெயர்';

  @override
  String get password => 'கடவுச்சொல்';

  @override
  String get loginNow => 'இப்போது உள்நுழைக';

  @override
  String welcomeUser(String name) {
    return 'வரவேற்கிறோம்! $name';
  }

  @override
  String get latestArticles => 'சமீபத்திய கட்டுரைகள்';

  @override
  String get organicFarmingTechniques => 'இயற்கை விவசாய முறைகள்';

  @override
  String get newCropVarieties => 'புதிய பயிர் வகைகள்';

  @override
  String get marketPricesUpdate => 'சந்தை விலை புதுப்பிப்பு';

  @override
  String get sampleArticleTitle => 'கட்டுரையின் தலைப்பு இங்கே செல்கிறது';

  @override
  String get changeLanguage => 'மொழியை மாற்று';

  @override
  String get home => 'முகப்பு';

  @override
  String get saved => 'சேமித்தவை';

  @override
  String get help => 'உதவி';

  @override
  String get profile => 'சுயவிவரம்';

  @override
  String get logout => 'வெளியேறு';

  @override
  String get logoutConfirmation => 'வெளியேற உறுதிப்படுத்தல்';

  @override
  String get logoutConfirmationMessage => 'நீங்கள் வெளியேற விரும்புகிறீர்களா?';

  @override
  String get cancel => 'ரத்து செய்';

  @override
  String get myQueries => 'எனது கேள்விகள்';

  @override
  String get askQuestion => 'ஒரு கேள்வியைக் கேளுங்கள்';

  @override
  String get noQueriesYet => 'இதுவரை கேள்விகள் எதுவும் இல்லை';

  @override
  String get createFirstQuery => 'எங்கள் நிபுணர்களிடம் உதவி பெற உங்கள் முதல் கேள்வியை உருவாக்கவும்';

  @override
  String get tryAgain => 'மீண்டும் முயற்சிக்கவும்';

  @override
  String get queries => 'கேள்விகள்';

  @override
  String get knowledge => 'அறிவு';

  @override
  String get authTokenNotFound => 'அங்கீகார டோக்கன் கிடைக்கவில்லை';

  @override
  String get failedToCreateQuery => 'கேள்வியை உருவாக்க முடியவில்லை';

  @override
  String get errorOccurred => 'பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.';

  @override
  String get retryLater => 'தயவுசெய்து பின்னர் மீண்டும் முயற்சிக்கவும்';

  @override
  String get marketPrices => 'சந்தை விலைகள்';

  @override
  String get failedToLoadMarketPrices => 'சந்தை விலைகளை ஏற்ற முடியவில்லை';

  @override
  String get noMarketPricesAvailable => 'சந்தை விலைகள் எதுவும் இல்லை';

  @override
  String get checkBackLater => 'சமீபத்திய விலைகளுக்கு பின்னர் பார்க்கவும்';

  @override
  String get priceHistory => 'விலை வரலாறு';

  @override
  String get currentMarketPrice => 'தற்போதைய சந்தை விலை';

  @override
  String get lastThirtyDaysPriceRange => 'கடந்த 30 நாட்களின் விலை வரம்பு';

  @override
  String get minPrice => 'குறைந்தபட்ச விலை';

  @override
  String get avgPrice => 'சராசரி விலை';

  @override
  String get maxPrice => 'அதிகபட்ச விலை';

  @override
  String get per => 'க்கு';

  @override
  String get close => 'மூடு';

  @override
  String get queryTitle => 'தலைப்பு';

  @override
  String get queryTitleHint => 'எ.கா., நெல்லில் பூச்சிகளைக் கட்டுப்படுத்துவது எப்படி?';

  @override
  String get queryDescription => 'விளக்கம்';

  @override
  String get queryDescriptionHint => 'உங்கள் பிரச்சினையை விரிவாக விவரிக்கவும்';

  @override
  String get addImage => 'படத்தைச் சேர்க்கவும்';

  @override
  String get addImageOptional => 'விருப்பமானது - உங்கள் கேள்விக்குத் தொடர்புடைய படத்தைச் சேர்க்கவும்';

  @override
  String get tapToAddImage => 'படத்தைச் சேர்க்க தட்டவும்';

  @override
  String get takePhoto => 'புகைப்படம் எடுக்கவும்';

  @override
  String get chooseFromGallery => 'கேலரியிலிருந்து தேர்வு செய்யவும்';

  @override
  String get submitQuery => 'கேள்வியைச் சமர்ப்பிக்கவும்';

  @override
  String get queryCreatedSuccess => 'கேள்வி வெற்றிகரமாக உருவாக்கப்பட்டது';

  @override
  String get fieldRequired => 'இந்த புலம் தேவை';

  @override
  String get queryDetails => 'கேள்வி விவரங்கள்';

  @override
  String get description => 'விளக்கம்';

  @override
  String get responses => 'பதில்கள்';

  @override
  String get noResponses => 'இதுவரை பதில்கள் இல்லை. எங்கள் நிபுணர்கள் விரைவில் உங்கள் கேள்விக்கு பதிலளிப்பார்கள்.';

  @override
  String get closeQuery => 'தீர்க்கப்பட்டதாகக் குறிக்கவும்';

  @override
  String get closeQueryTitle => 'தீர்க்கப்பட்டதாகக் குறிக்கவும்';

  @override
  String get closeQueryMessage => 'இந்தக் கேள்வியைத் தீர்க்கப்பட்டதாகக் குறிக்க விரும்புகிறீர்களா?';

  @override
  String get queryClosedSuccess => 'கேள்வி வெற்றிகரமாக தீர்க்கப்பட்டதாகக் குறிக்கப்பட்டது';

  @override
  String get queryNotFound => 'கேள்வி கிடைக்கவில்லை';

  @override
  String get statusOpen => 'திறந்த';

  @override
  String get statusInProgress => 'செயல்பாட்டில் உள்ளது';

  @override
  String get statusResolved => 'தீர்க்கப்பட்டது';

  @override
  String get statusClosed => 'மூடப்பட்டது';

  @override
  String get editProfile => 'சுயவிவரத்தைத் திருத்து';

  @override
  String get contactInformation => 'தொடர்பு தகவல்';

  @override
  String get addressInformation => 'முகவரி தகவல்';

  @override
  String get mobileNumber => 'கைபேசி எண்';

  @override
  String get alternateNumber => 'மாற்று எண்';

  @override
  String get email => 'மின்னஞ்சல்';

  @override
  String get addressLine1 => 'முகவரி வரி 1';

  @override
  String get addressLine2 => 'முகவரி வரி 2';

  @override
  String get locality => 'பகுதி';

  @override
  String get pincode => 'அஞ்சல் குறியீடு';

  @override
  String get invalidEmail => 'சரியான மின்னஞ்சல் முகவரியை உள்ளிடவும்';

  @override
  String get invalidPincode => 'சரியான 6-இலக்க அஞ்சல் குறியீட்டை உள்ளிடவும்';

  @override
  String get save => 'சேமி';

  @override
  String get profileUpdated => 'சுயவிவரம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get profilePictureUpdated => 'சுயவிவரப் படம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get announcement => 'அறிவிப்பு';

  @override
  String get announcements => 'அறிவிப்புகள்';

  @override
  String get errorLoadingAnnouncement => 'அறிவிப்பை ஏற்றுவதில் பிழை';

  @override
  String get announcementNotFound => 'அறிவிப்பு கிடைக்கவில்லை';

  @override
  String get imageUnavailable => 'படம் கிடைக்கவில்லை';

  @override
  String get failedToLoadAnnouncements => 'அறிவிப்புகளை ஏற்ற முடியவில்லை';

  @override
  String get noAnnouncementsAvailable => 'அறிவிப்புகள் எதுவும் இல்லை';

  @override
  String get checkBackLaterForUpdates => 'புதுப்பிப்புகளுக்கு பின்னர் சரிபார்க்கவும்';

  @override
  String get failedToLoadMoreAnnouncements => 'மேலும் அறிவிப்புகளை ஏற்ற முடியவில்லை';

  @override
  String get transactionActivity => 'பரிவர்த்தனை செயல்பாடு';

  @override
  String get totalTransactions => 'மொத்த பரிவர்த்தனைகள்';

  @override
  String get totalValue => 'மொத்த மதிப்பு';

  @override
  String get monthlyAnalysis => 'மாதாந்திர பகுப்பாய்வு';

  @override
  String get transactionStatus => 'பரிவர்த்தனை நிலை';

  @override
  String get loadingTransactionData => 'பரிவர்த்தனை தரவை ஏற்றுகிறது...';

  @override
  String get failedToLoadTransactionStats => 'பரிவர்த்தனை புள்ளிவிவரங்களை ஏற்ற முடியவில்லை';

  @override
  String get unknownError => 'அறியப்படாத பிழை ஏற்பட்டது';

  @override
  String get noStatusDataAvailable => 'நிலை தரவு எதுவும் கிடைக்கவில்லை';

  @override
  String get viewTransactions => 'பரிவர்த்தனைகளைக் காண்க';

  @override
  String get recentTransactions => 'சமீபத்திய பரிவர்த்தனைகள்';

  @override
  String get viewAll => 'அனைத்தையும் காட்டு';

  @override
  String get failedToLoadTransactions => 'பரிவர்த்தனைகளை ஏற்ற முடியவில்லை';

  @override
  String get retry => 'மீண்டும் முயற்சிக்கவும்';

  @override
  String get noTransactionsYet => 'இதுவரை பரிவர்த்தனைகள் இல்லை';

  @override
  String get transactionsWillAppearHere => 'உங்கள் சமீபத்திய பரிவர்த்தனைகள் இங்கே தோன்றும்';

  @override
  String get statusAccepted => 'ஏற்றுக்கொள்ளப்பட்டது';

  @override
  String get statusDelivered => 'வழங்கப்பட்டது';

  @override
  String get statusCancelled => 'ரத்து செய்யப்பட்டது';

  @override
  String get myProduce => 'எனது விளைபொருட்கள்';

  @override
  String get all => 'அனைத்தும்';

  @override
  String get available => 'கிடைக்கக்கூடியது';

  @override
  String get pending => 'நிலுவையில் உள்ளது';

  @override
  String get expired => 'காலாவதியானது';

  @override
  String get sold => 'விற்கப்பட்டது';

  @override
  String get addNewProduce => 'புதிய விளைபொருளைச் சேர்க்கவும்';

  @override
  String get addProduce => 'விளைபொருளைச் சேர்க்கவும்';

  @override
  String get edit => 'திருத்து';

  @override
  String get delete => 'நீக்கு';

  @override
  String get markAsExpired => 'காலாவதியானதாகக் குறிக்கவும்';

  @override
  String get markAsSold => 'விற்கப்பட்டதாகக் குறிக்கவும்';

  @override
  String get markAsAvailable => 'கிடைக்கக்கூடியதாகக் குறிக்கவும்';

  @override
  String get updateProduceStatus => 'நிலையைப் புதுப்பிக்கிறது...';

  @override
  String get produceStatusUpdated => 'விளைபொருள் நிலை வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get failedToUpdateStatus => 'விளைபொருள் நிலையைப் புதுப்பிக்க முடியவில்லை';

  @override
  String get deleteProduceTitle => 'விளைபொருள் பட்டியலை நீக்கு';

  @override
  String get deleteProduceConfirm => 'இந்த விளைபொருள் பட்டியலை நீக்க விரும்புகிறீர்களா?';

  @override
  String get deleting => 'நீக்குகிறது...';

  @override
  String get produceDeletedSuccess => 'விளைபொருள் பட்டியல் வெற்றிகரமாக நீக்கப்பட்டது';

  @override
  String get failedToDeleteProduce => 'விளைபொருள் பட்டியலை நீக்க முடியவில்லை';

  @override
  String get failedToLoadProduce => 'விளைபொருள் பட்டியல்களை ஏற்ற முடியவில்லை';

  @override
  String get failedToLoadMoreProduce => 'மேலும் விளைபொருள் பட்டியல்களை ஏற்ற முடியவில்லை';

  @override
  String get noProduceListings => 'இன்னும் விளைபொருள் பட்டியல்கள் இல்லை';

  @override
  String get addFirstProduce => 'விற்பனை செய்யத் தொடங்க உங்கள் முதல் விளைபொருள் பட்டியலைச் சேர்க்கவும்';

  @override
  String noFilteredProduceListings(String filter) {
    return '$filter விளைபொருள் பட்டியல்கள் இல்லை';
  }

  @override
  String get changeFilterOrAdd => 'வடிகட்டியை மாற்றவும் அல்லது புதிய விளைபொருட்களைச் சேர்க்கவும்';

  @override
  String get showAllProduce => 'அனைத்து விளைபொருட்களையும் காட்டு';

  @override
  String availableFrom(String date) {
    return '$date முதல் கிடைக்கும்';
  }

  @override
  String posted(String date) {
    return 'பதிவிடப்பட்டது: $date';
  }

  @override
  String interestedCount(int count) {
    return '$count பேர் ஆர்வமுள்ளவர்கள்';
  }

  @override
  String get longPressToDebug => 'பிழைத்திருத்த நீண்ட நேரம் அழுத்தவும்';

  @override
  String get tapToZoom => 'பெரிதாக்க தட்டவும்';

  @override
  String get bulkUpdate => 'மொத்த புதுப்பிப்பு';

  @override
  String get bulkUpdateQuestion => 'காலாவதியான அனைத்து விளைபொருள் பட்டியல்களையும் புதுப்பிக்கவா?';

  @override
  String get update => 'புதுப்பி';

  @override
  String get failedToLoadImage => 'படத்தை ஏற்ற முடியவில்லை';

  @override
  String get editProduce => 'விளைபொருளைத் திருத்து';

  @override
  String get selectCrop => 'தயவுசெய்து ஒரு பயிரைத் தேர்ந்தெடுக்கவும்';

  @override
  String get failedToUpdateProduce => 'விளைபொருளை புதுப்பிக்க முடியவில்லை';

  @override
  String get failedToCreateProduce => 'விளைபொருளை உருவாக்க முடியவில்லை';

  @override
  String get crops => 'பயிர்கள்';

  @override
  String get unit => 'அலகு';

  @override
  String get availability => 'கிடைக்கும் தன்மை';

  @override
  String get images => 'படங்கள்';

  @override
  String get updateProduce => 'விளைபொருளைப் புதுப்பி';

  @override
  String get createProduce => 'விளைபொருளை உருவாக்கு';

  @override
  String get errorProcessingImage => 'படத்தை செயலாக்குவதில் பிழை. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.';

  @override
  String get addPrimaryImage => 'தயவுசெய்து முதன்மை படத்தைச் சேர்க்கவும்';

  @override
  String get fulfillingRequirement => 'தேவைகளை நிறைவேற்றுதல்';

  @override
  String get produceDetails => 'விளைபொருள் விவரங்கள்';

  @override
  String get availabilityDates => 'கிடைக்கும் தேதிகள்';

  @override
  String get productImages => 'தயாரிப்பு படங்கள்';

  @override
  String get varietyOptional => 'வகை (விருப்பத்தேர்வு)';

  @override
  String get quantity => 'அளவு';

  @override
  String get descriptionOptional => 'விளக்கம் (விருப்பத்தேர்வு)';

  @override
  String get availableUntilOptional => 'வரை கிடைக்கும் (விருப்பத்தேர்வு)';

  @override
  String get harvestDateOptional => 'அறுவடை தேதி (விருப்பத்தேர்வு)';

  @override
  String get primaryImageRequired => 'முதன்மை படம் (தேவை)';

  @override
  String get secondaryImageOptional => 'இரண்டாம் படம் (விருப்பத்தேர்வு)';

  @override
  String get tertiaryImageOptional => 'மூன்றாம் படம் (விருப்பத்தேர்வு)';

  @override
  String get varietyHint => 'எ.கா., பாஸ்மதி, ரோமா';

  @override
  String get enterQuantity => 'அளவை உள்ளிடவும்';

  @override
  String get describeProduceHint => 'உங்கள் விளைபொருள் தரம், தோற்றம் போன்றவற்றை விவரிக்கவும்';

  @override
  String get quantityRequired => 'அளவு தேவை';

  @override
  String get enterValidNumber => 'தயவுசெய்து சரியான எண்ணை உள்ளிடவும்';

  @override
  String get quantityGreaterThanZero => 'அளவு 0ஐ விட அதிகமாக இருக்க வேண்டும்';

  @override
  String get notSpecified => 'குறிப்பிடப்படவில்லை';

  @override
  String get transactionSummary => 'பரிவர்த்தனை சுருக்கம்';

  @override
  String get transactions => 'பரிவர்த்தனைகள்';

  @override
  String pricePer(String unitType) {
    return '$unitTypeக்கு விலை';
  }

  @override
  String get totalAmount => 'மொத்த தொகை';

  @override
  String get createProduceAndExpress => 'விளைபொருளை உருவாக்கி ஆர்வத்தை வெளிப்படுத்து';

  @override
  String get requiredTapToAddImage => 'தேவை - படத்தைச் சேர்க்க தட்டவும்';

  @override
  String get primaryImageRequiredError => 'முதன்மை படம் தேவை';

  @override
  String get whatHappensNext => 'அடுத்து என்ன நடக்கும்?';

  @override
  String get afterSubmittingInfo => 'சமர்ப்பித்த பிறகு, உங்கள் விளைபொருள் உருவாக்கப்பட்டு தானாகவே இந்த தேவைக்கு இணைக்கப்படும். உங்கள் ஆர்வம் பற்றி FPO அறிவிக்கப்படும் மற்றும் உங்கள் சலுகையை மதிப்பாய்வு செய்யும்.';

  @override
  String get produceCreatedSuccess => 'விளைபொருள் வெற்றிகரமாக உருவாக்கப்பட்டு ஆர்வம் வெளிப்படுத்தப்பட்டது!';
}
