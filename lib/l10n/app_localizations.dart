import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ta.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ta')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'FPO TRADERS'**
  String get appTitle;

  /// No description provided for @languageSelectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Select your Preferred language'**
  String get languageSelectionTitle;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @tamil.
  ///
  /// In en, this message translates to:
  /// **'தமிழ்'**
  String get tamil;

  /// No description provided for @loginToYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Login to your account'**
  String get loginToYourAccount;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @loginNow.
  ///
  /// In en, this message translates to:
  /// **'Login now'**
  String get loginNow;

  /// No description provided for @welcomeUser.
  ///
  /// In en, this message translates to:
  /// **'Welcome! {name}'**
  String welcomeUser(String name);

  /// No description provided for @latestArticles.
  ///
  /// In en, this message translates to:
  /// **'Latest articles'**
  String get latestArticles;

  /// No description provided for @organicFarmingTechniques.
  ///
  /// In en, this message translates to:
  /// **'Organic Farming Techniques'**
  String get organicFarmingTechniques;

  /// No description provided for @newCropVarieties.
  ///
  /// In en, this message translates to:
  /// **'New Crop Varieties'**
  String get newCropVarieties;

  /// No description provided for @marketPricesUpdate.
  ///
  /// In en, this message translates to:
  /// **'Market Prices Update'**
  String get marketPricesUpdate;

  /// No description provided for @sampleArticleTitle.
  ///
  /// In en, this message translates to:
  /// **'Title of the Article Goes Here'**
  String get sampleArticleTitle;

  /// No description provided for @changeLanguage.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguage;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @saved.
  ///
  /// In en, this message translates to:
  /// **'Saved'**
  String get saved;

  /// No description provided for @help.
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @logoutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Logout Confirmation'**
  String get logoutConfirmation;

  /// No description provided for @logoutConfirmationMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get logoutConfirmationMessage;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @myQueries.
  ///
  /// In en, this message translates to:
  /// **'My Queries'**
  String get myQueries;

  /// No description provided for @askQuestion.
  ///
  /// In en, this message translates to:
  /// **'Ask a Question'**
  String get askQuestion;

  /// No description provided for @noQueriesYet.
  ///
  /// In en, this message translates to:
  /// **'No queries yet'**
  String get noQueriesYet;

  /// No description provided for @createFirstQuery.
  ///
  /// In en, this message translates to:
  /// **'Create your first query to get help from our experts'**
  String get createFirstQuery;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @queries.
  ///
  /// In en, this message translates to:
  /// **'Queries'**
  String get queries;

  /// No description provided for @knowledge.
  ///
  /// In en, this message translates to:
  /// **'Knowledge'**
  String get knowledge;

  /// No description provided for @authTokenNotFound.
  ///
  /// In en, this message translates to:
  /// **'Authentication token not found'**
  String get authTokenNotFound;

  /// No description provided for @failedToCreateQuery.
  ///
  /// In en, this message translates to:
  /// **'Failed to create query'**
  String get failedToCreateQuery;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'Error occurred. Please try again.'**
  String get errorOccurred;

  /// No description provided for @retryLater.
  ///
  /// In en, this message translates to:
  /// **'Please try again later'**
  String get retryLater;

  /// No description provided for @marketPrices.
  ///
  /// In en, this message translates to:
  /// **'Market Prices'**
  String get marketPrices;

  /// No description provided for @failedToLoadMarketPrices.
  ///
  /// In en, this message translates to:
  /// **'Failed to load market prices'**
  String get failedToLoadMarketPrices;

  /// No description provided for @noMarketPricesAvailable.
  ///
  /// In en, this message translates to:
  /// **'No market prices available'**
  String get noMarketPricesAvailable;

  /// No description provided for @checkBackLater.
  ///
  /// In en, this message translates to:
  /// **'Check back later for the latest prices'**
  String get checkBackLater;

  /// No description provided for @priceHistory.
  ///
  /// In en, this message translates to:
  /// **'Price History'**
  String get priceHistory;

  /// No description provided for @currentMarketPrice.
  ///
  /// In en, this message translates to:
  /// **'Current Market Price'**
  String get currentMarketPrice;

  /// No description provided for @lastThirtyDaysPriceRange.
  ///
  /// In en, this message translates to:
  /// **'Last 30 Days Price Range'**
  String get lastThirtyDaysPriceRange;

  /// No description provided for @minPrice.
  ///
  /// In en, this message translates to:
  /// **'Min Price'**
  String get minPrice;

  /// No description provided for @avgPrice.
  ///
  /// In en, this message translates to:
  /// **'Avg Price'**
  String get avgPrice;

  /// No description provided for @maxPrice.
  ///
  /// In en, this message translates to:
  /// **'Max Price'**
  String get maxPrice;

  /// No description provided for @per.
  ///
  /// In en, this message translates to:
  /// **'per'**
  String get per;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @queryTitle.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get queryTitle;

  /// No description provided for @queryTitleHint.
  ///
  /// In en, this message translates to:
  /// **'E.g., How to control pests in rice?'**
  String get queryTitleHint;

  /// No description provided for @queryDescription.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get queryDescription;

  /// No description provided for @queryDescriptionHint.
  ///
  /// In en, this message translates to:
  /// **'Describe your issue in detail'**
  String get queryDescriptionHint;

  /// No description provided for @addImage.
  ///
  /// In en, this message translates to:
  /// **'Add Image'**
  String get addImage;

  /// No description provided for @addImageOptional.
  ///
  /// In en, this message translates to:
  /// **'Optional - Add an image related to your query'**
  String get addImageOptional;

  /// No description provided for @tapToAddImage.
  ///
  /// In en, this message translates to:
  /// **'Tap to add an image'**
  String get tapToAddImage;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take a photo'**
  String get takePhoto;

  /// No description provided for @chooseFromGallery.
  ///
  /// In en, this message translates to:
  /// **'Choose from gallery'**
  String get chooseFromGallery;

  /// No description provided for @submitQuery.
  ///
  /// In en, this message translates to:
  /// **'Submit Query'**
  String get submitQuery;

  /// No description provided for @queryCreatedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Query created successfully'**
  String get queryCreatedSuccess;

  /// No description provided for @fieldRequired.
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get fieldRequired;

  /// No description provided for @queryDetails.
  ///
  /// In en, this message translates to:
  /// **'Query Details'**
  String get queryDetails;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @responses.
  ///
  /// In en, this message translates to:
  /// **'Responses'**
  String get responses;

  /// No description provided for @noResponses.
  ///
  /// In en, this message translates to:
  /// **'No responses yet. Our experts will respond to your query soon.'**
  String get noResponses;

  /// No description provided for @closeQuery.
  ///
  /// In en, this message translates to:
  /// **'Mark as Resolved'**
  String get closeQuery;

  /// No description provided for @closeQueryTitle.
  ///
  /// In en, this message translates to:
  /// **'Mark as Resolved'**
  String get closeQueryTitle;

  /// No description provided for @closeQueryMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to mark this query as resolved?'**
  String get closeQueryMessage;

  /// No description provided for @queryClosedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Query marked as resolved successfully'**
  String get queryClosedSuccess;

  /// No description provided for @queryNotFound.
  ///
  /// In en, this message translates to:
  /// **'Query not found'**
  String get queryNotFound;

  /// No description provided for @statusOpen.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get statusOpen;

  /// No description provided for @statusInProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get statusInProgress;

  /// No description provided for @statusResolved.
  ///
  /// In en, this message translates to:
  /// **'Resolved'**
  String get statusResolved;

  /// No description provided for @statusClosed.
  ///
  /// In en, this message translates to:
  /// **'Closed'**
  String get statusClosed;

  /// No description provided for @editProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// No description provided for @contactInformation.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactInformation;

  /// No description provided for @addressInformation.
  ///
  /// In en, this message translates to:
  /// **'Address Information'**
  String get addressInformation;

  /// No description provided for @mobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Mobile Number'**
  String get mobileNumber;

  /// No description provided for @alternateNumber.
  ///
  /// In en, this message translates to:
  /// **'Alternate Number'**
  String get alternateNumber;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @addressLine1.
  ///
  /// In en, this message translates to:
  /// **'Address Line 1'**
  String get addressLine1;

  /// No description provided for @addressLine2.
  ///
  /// In en, this message translates to:
  /// **'Address Line 2'**
  String get addressLine2;

  /// No description provided for @locality.
  ///
  /// In en, this message translates to:
  /// **'Locality'**
  String get locality;

  /// No description provided for @pincode.
  ///
  /// In en, this message translates to:
  /// **'Pincode'**
  String get pincode;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get invalidEmail;

  /// No description provided for @invalidPincode.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid 6-digit pincode'**
  String get invalidPincode;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @profileUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdated;

  /// No description provided for @profilePictureUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile picture updated successfully'**
  String get profilePictureUpdated;

  /// No description provided for @announcement.
  ///
  /// In en, this message translates to:
  /// **'Announcement'**
  String get announcement;

  /// No description provided for @announcements.
  ///
  /// In en, this message translates to:
  /// **'Announcements'**
  String get announcements;

  /// No description provided for @errorLoadingAnnouncement.
  ///
  /// In en, this message translates to:
  /// **'Error loading announcement'**
  String get errorLoadingAnnouncement;

  /// No description provided for @announcementNotFound.
  ///
  /// In en, this message translates to:
  /// **'Announcement not found'**
  String get announcementNotFound;

  /// No description provided for @imageUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Image unavailable'**
  String get imageUnavailable;

  /// No description provided for @failedToLoadAnnouncements.
  ///
  /// In en, this message translates to:
  /// **'Failed to load announcements'**
  String get failedToLoadAnnouncements;

  /// No description provided for @noAnnouncementsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No announcements available'**
  String get noAnnouncementsAvailable;

  /// No description provided for @checkBackLaterForUpdates.
  ///
  /// In en, this message translates to:
  /// **'Check back later for updates'**
  String get checkBackLaterForUpdates;

  /// No description provided for @failedToLoadMoreAnnouncements.
  ///
  /// In en, this message translates to:
  /// **'Failed to load more announcements'**
  String get failedToLoadMoreAnnouncements;

  /// No description provided for @transactionActivity.
  ///
  /// In en, this message translates to:
  /// **'Transaction Activity'**
  String get transactionActivity;

  /// No description provided for @totalTransactions.
  ///
  /// In en, this message translates to:
  /// **'Total Transactions'**
  String get totalTransactions;

  /// No description provided for @totalValue.
  ///
  /// In en, this message translates to:
  /// **'Total Value'**
  String get totalValue;

  /// No description provided for @monthlyAnalysis.
  ///
  /// In en, this message translates to:
  /// **'Monthly Analysis'**
  String get monthlyAnalysis;

  /// No description provided for @transactionStatus.
  ///
  /// In en, this message translates to:
  /// **'Transaction Status'**
  String get transactionStatus;

  /// No description provided for @loadingTransactionData.
  ///
  /// In en, this message translates to:
  /// **'Loading transaction data...'**
  String get loadingTransactionData;

  /// No description provided for @failedToLoadTransactionStats.
  ///
  /// In en, this message translates to:
  /// **'Failed to load transaction statistics'**
  String get failedToLoadTransactionStats;

  /// No description provided for @unknownError.
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred'**
  String get unknownError;

  /// No description provided for @noStatusDataAvailable.
  ///
  /// In en, this message translates to:
  /// **'No status data available'**
  String get noStatusDataAvailable;

  /// No description provided for @viewTransactions.
  ///
  /// In en, this message translates to:
  /// **'View Transactions'**
  String get viewTransactions;

  /// No description provided for @recentTransactions.
  ///
  /// In en, this message translates to:
  /// **'Recent Transactions'**
  String get recentTransactions;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @failedToLoadTransactions.
  ///
  /// In en, this message translates to:
  /// **'Failed to load transactions'**
  String get failedToLoadTransactions;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @noTransactionsYet.
  ///
  /// In en, this message translates to:
  /// **'No transactions yet'**
  String get noTransactionsYet;

  /// No description provided for @transactionsWillAppearHere.
  ///
  /// In en, this message translates to:
  /// **'Your recent transactions will appear here'**
  String get transactionsWillAppearHere;

  /// No description provided for @statusAccepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get statusAccepted;

  /// No description provided for @statusDelivered.
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get statusDelivered;

  /// No description provided for @statusCancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get statusCancelled;

  /// No description provided for @myProduce.
  ///
  /// In en, this message translates to:
  /// **'My Produce'**
  String get myProduce;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @expired.
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// No description provided for @sold.
  ///
  /// In en, this message translates to:
  /// **'Sold'**
  String get sold;

  /// No description provided for @addNewProduce.
  ///
  /// In en, this message translates to:
  /// **'Add New Produce'**
  String get addNewProduce;

  /// No description provided for @addProduce.
  ///
  /// In en, this message translates to:
  /// **'Add Produce'**
  String get addProduce;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @markAsExpired.
  ///
  /// In en, this message translates to:
  /// **'Mark as Expired'**
  String get markAsExpired;

  /// No description provided for @markAsSold.
  ///
  /// In en, this message translates to:
  /// **'Mark as Sold'**
  String get markAsSold;

  /// No description provided for @markAsAvailable.
  ///
  /// In en, this message translates to:
  /// **'Mark as Available'**
  String get markAsAvailable;

  /// No description provided for @updateProduceStatus.
  ///
  /// In en, this message translates to:
  /// **'Updating status...'**
  String get updateProduceStatus;

  /// No description provided for @produceStatusUpdated.
  ///
  /// In en, this message translates to:
  /// **'Produce status updated successfully'**
  String get produceStatusUpdated;

  /// No description provided for @failedToUpdateStatus.
  ///
  /// In en, this message translates to:
  /// **'Failed to update produce status'**
  String get failedToUpdateStatus;

  /// No description provided for @deleteProduceTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Produce Listing'**
  String get deleteProduceTitle;

  /// No description provided for @deleteProduceConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this produce listing?'**
  String get deleteProduceConfirm;

  /// No description provided for @deleting.
  ///
  /// In en, this message translates to:
  /// **'Deleting...'**
  String get deleting;

  /// No description provided for @produceDeletedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Produce listing deleted successfully'**
  String get produceDeletedSuccess;

  /// No description provided for @failedToDeleteProduce.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete produce listing'**
  String get failedToDeleteProduce;

  /// No description provided for @failedToLoadProduce.
  ///
  /// In en, this message translates to:
  /// **'Failed to load produce listings'**
  String get failedToLoadProduce;

  /// No description provided for @failedToLoadMoreProduce.
  ///
  /// In en, this message translates to:
  /// **'Failed to load more produce listings'**
  String get failedToLoadMoreProduce;

  /// No description provided for @noProduceListings.
  ///
  /// In en, this message translates to:
  /// **'No produce listings yet'**
  String get noProduceListings;

  /// No description provided for @addFirstProduce.
  ///
  /// In en, this message translates to:
  /// **'Add your first produce listing to start selling'**
  String get addFirstProduce;

  /// No description provided for @noFilteredProduceListings.
  ///
  /// In en, this message translates to:
  /// **'No {filter} produce listings'**
  String noFilteredProduceListings(String filter);

  /// No description provided for @changeFilterOrAdd.
  ///
  /// In en, this message translates to:
  /// **'Change the filter or add new produce'**
  String get changeFilterOrAdd;

  /// No description provided for @showAllProduce.
  ///
  /// In en, this message translates to:
  /// **'Show All Produce'**
  String get showAllProduce;

  /// No description provided for @availableFrom.
  ///
  /// In en, this message translates to:
  /// **'Available from '**
  String availableFrom(String date);

  /// No description provided for @posted.
  ///
  /// In en, this message translates to:
  /// **'Posted: {date}'**
  String posted(String date);

  /// No description provided for @interestedCount.
  ///
  /// In en, this message translates to:
  /// **'{count} interested'**
  String interestedCount(int count);

  /// No description provided for @longPressToDebug.
  ///
  /// In en, this message translates to:
  /// **'Long press to debug'**
  String get longPressToDebug;

  /// No description provided for @tapToZoom.
  ///
  /// In en, this message translates to:
  /// **'Tap to zoom'**
  String get tapToZoom;

  /// No description provided for @bulkUpdate.
  ///
  /// In en, this message translates to:
  /// **'Bulk Update'**
  String get bulkUpdate;

  /// No description provided for @bulkUpdateQuestion.
  ///
  /// In en, this message translates to:
  /// **'Update all expired produce listings?'**
  String get bulkUpdateQuestion;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @failedToLoadImage.
  ///
  /// In en, this message translates to:
  /// **'Failed to load image'**
  String get failedToLoadImage;

  /// No description provided for @editProduce.
  ///
  /// In en, this message translates to:
  /// **'Edit Produce'**
  String get editProduce;

  /// No description provided for @selectCrop.
  ///
  /// In en, this message translates to:
  /// **'Please select a crop'**
  String get selectCrop;

  /// No description provided for @failedToUpdateProduce.
  ///
  /// In en, this message translates to:
  /// **'Failed to update produce'**
  String get failedToUpdateProduce;

  /// No description provided for @failedToCreateProduce.
  ///
  /// In en, this message translates to:
  /// **'Failed to create produce'**
  String get failedToCreateProduce;

  /// No description provided for @crops.
  ///
  /// In en, this message translates to:
  /// **'Crops'**
  String get crops;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Unit'**
  String get unit;

  /// No description provided for @availability.
  ///
  /// In en, this message translates to:
  /// **'Availability'**
  String get availability;

  /// No description provided for @images.
  ///
  /// In en, this message translates to:
  /// **'Images'**
  String get images;

  /// No description provided for @updateProduce.
  ///
  /// In en, this message translates to:
  /// **'Update Produce'**
  String get updateProduce;

  /// No description provided for @createProduce.
  ///
  /// In en, this message translates to:
  /// **'Create Produce'**
  String get createProduce;

  /// No description provided for @errorProcessingImage.
  ///
  /// In en, this message translates to:
  /// **'Error processing image. Please try again.'**
  String get errorProcessingImage;

  /// No description provided for @addPrimaryImage.
  ///
  /// In en, this message translates to:
  /// **'Please add a primary image'**
  String get addPrimaryImage;

  /// No description provided for @fulfillingRequirement.
  ///
  /// In en, this message translates to:
  /// **'Fulfilling Requirement'**
  String get fulfillingRequirement;

  /// No description provided for @produceDetails.
  ///
  /// In en, this message translates to:
  /// **'Produce Details'**
  String get produceDetails;

  /// No description provided for @availabilityDates.
  ///
  /// In en, this message translates to:
  /// **'Availability Dates'**
  String get availabilityDates;

  /// No description provided for @productImages.
  ///
  /// In en, this message translates to:
  /// **'Product Images'**
  String get productImages;

  /// No description provided for @varietyOptional.
  ///
  /// In en, this message translates to:
  /// **'Variety (Optionals)'**
  String get varietyOptional;

  /// No description provided for @quantity.
  ///
  /// In en, this message translates to:
  /// **'Quantity'**
  String get quantity;

  /// No description provided for @descriptionOptional.
  ///
  /// In en, this message translates to:
  /// **'Description (Optional)'**
  String get descriptionOptional;

  /// No description provided for @availableUntilOptional.
  ///
  /// In en, this message translates to:
  /// **'Available Until (Optional)'**
  String get availableUntilOptional;

  /// No description provided for @harvestDateOptional.
  ///
  /// In en, this message translates to:
  /// **'Harvest Date (Optional)'**
  String get harvestDateOptional;

  /// No description provided for @primaryImageRequired.
  ///
  /// In en, this message translates to:
  /// **'Primary Image (Required)'**
  String get primaryImageRequired;

  /// No description provided for @secondaryImageOptional.
  ///
  /// In en, this message translates to:
  /// **'Secondary Image (Optional)'**
  String get secondaryImageOptional;

  /// No description provided for @tertiaryImageOptional.
  ///
  /// In en, this message translates to:
  /// **'Tertiary Image (Optional)'**
  String get tertiaryImageOptional;

  /// No description provided for @varietyHint.
  ///
  /// In en, this message translates to:
  /// **'E.g., Basmati, Roma'**
  String get varietyHint;

  /// No description provided for @enterQuantity.
  ///
  /// In en, this message translates to:
  /// **'Enter quantity'**
  String get enterQuantity;

  /// No description provided for @describeProduceHint.
  ///
  /// In en, this message translates to:
  /// **'Describe your produce qualities, origin, etc.'**
  String get describeProduceHint;

  /// No description provided for @quantityRequired.
  ///
  /// In en, this message translates to:
  /// **'Quantity is required'**
  String get quantityRequired;

  /// No description provided for @enterValidNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid number'**
  String get enterValidNumber;

  /// No description provided for @quantityGreaterThanZero.
  ///
  /// In en, this message translates to:
  /// **'Quantity must be greater than 0'**
  String get quantityGreaterThanZero;

  /// No description provided for @notSpecified.
  ///
  /// In en, this message translates to:
  /// **'Not specified'**
  String get notSpecified;

  /// No description provided for @transactionSummary.
  ///
  /// In en, this message translates to:
  /// **'Transaction Summary'**
  String get transactionSummary;

  /// No description provided for @transactions.
  ///
  /// In en, this message translates to:
  /// **'Transactions'**
  String get transactions;

  /// No description provided for @pricePer.
  ///
  /// In en, this message translates to:
  /// **'Price per {unitType}'**
  String pricePer(String unitType);

  /// No description provided for @totalAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get totalAmount;

  /// No description provided for @createProduceAndExpress.
  ///
  /// In en, this message translates to:
  /// **'Create Produce & Express Interest'**
  String get createProduceAndExpress;

  /// No description provided for @requiredTapToAddImage.
  ///
  /// In en, this message translates to:
  /// **'Required - Tap to add image'**
  String get requiredTapToAddImage;

  /// No description provided for @primaryImageRequiredError.
  ///
  /// In en, this message translates to:
  /// **'Primary image is required'**
  String get primaryImageRequiredError;

  /// No description provided for @whatHappensNext.
  ///
  /// In en, this message translates to:
  /// **'What happens next?'**
  String get whatHappensNext;

  /// No description provided for @afterSubmittingInfo.
  ///
  /// In en, this message translates to:
  /// **'After submitting, your produce will be created and automatically linked to this requirement. The FPO will be notified of your interest and will review your offer.'**
  String get afterSubmittingInfo;

  /// No description provided for @produceCreatedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Produce created and interest expressed successfully!'**
  String get produceCreatedSuccess;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ta'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ta': return AppLocalizationsTa();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
