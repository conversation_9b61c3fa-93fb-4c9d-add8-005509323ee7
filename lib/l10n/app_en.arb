{"@@locale": "en", "appTitle": "FPO TRADERS", "languageSelectionTitle": "Select your Preferred language", "english": "English", "tamil": "தமிழ்", "loginToYourAccount": "Login to your account", "username": "Username", "password": "Password", "loginNow": "Login now", "welcomeUser": "Welcome! {name}", "@welcomeUser": {"placeholders": {"name": {"type": "String"}}}, "latestArticles": "Latest articles", "organicFarmingTechniques": "Organic Farming Techniques", "newCropVarieties": "New Crop Varieties", "marketPricesUpdate": "Market Prices Update", "sampleArticleTitle": "Title of the Article Goes Here", "changeLanguage": "Change Language", "home": "Home", "saved": "Saved", "help": "Help", "profile": "Profile", "logout": "Logout", "logoutConfirmation": "Logout Confirmation", "logoutConfirmationMessage": "Are you sure you want to log out?", "cancel": "Cancel", "myQueries": "My Queries", "askQuestion": "Ask a Question", "noQueriesYet": "No queries yet", "createFirstQuery": "Create your first query to get help from our experts", "tryAgain": "Try Again", "queries": "Queries", "knowledge": "Knowledge", "authTokenNotFound": "Authentication token not found", "failedToCreateQuery": "Failed to create query", "errorOccurred": "Error occurred. Please try again.", "retryLater": "Please try again later", "marketPrices": "Market Prices", "failedToLoadMarketPrices": "Failed to load market prices", "noMarketPricesAvailable": "No market prices available", "checkBackLater": "Check back later for the latest prices", "priceHistory": "Price History", "currentMarketPrice": "Current Market Price", "lastThirtyDaysPriceRange": "Last 30 Days Price Range", "minPrice": "<PERSON>", "avgPrice": "Avg Price", "maxPrice": "Max Price", "per": "per", "close": "Close", "queryTitle": "Title", "queryTitleHint": "E.g., How to control pests in rice?", "queryDescription": "Description", "queryDescriptionHint": "Describe your issue in detail", "addImage": "Add Image", "addImageOptional": "Optional - Add an image related to your query", "tapToAddImage": "Tap to add an image", "takePhoto": "Take a photo", "chooseFromGallery": "Choose from gallery", "submitQuery": "Submit Query", "queryCreatedSuccess": "Query created successfully", "fieldRequired": "This field is required", "queryDetails": "Query Details", "description": "Description", "responses": "Responses", "noResponses": "No responses yet. Our experts will respond to your query soon.", "closeQuery": "<PERSON> as Resolved", "closeQueryTitle": "<PERSON> as Resolved", "closeQueryMessage": "Are you sure you want to mark this query as resolved?", "queryClosedSuccess": "Query marked as resolved successfully", "queryNotFound": "Query not found", "statusOpen": "Open", "statusInProgress": "In Progress", "statusResolved": "Resolved", "statusClosed": "Closed", "editProfile": "Edit Profile", "contactInformation": "Contact Information", "addressInformation": "Address Information", "mobileNumber": "Mobile Number", "alternateNumber": "Alternate Number", "email": "Email", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "locality": "Locality", "pincode": "Pincode", "invalidEmail": "Please enter a valid email address", "invalidPincode": "Please enter a valid 6-digit pincode", "save": "Save", "profileUpdated": "Profile updated successfully", "profilePictureUpdated": "Profile picture updated successfully", "announcement": "Announcement", "announcements": "Announcements", "errorLoadingAnnouncement": "Error loading announcement", "announcementNotFound": "Announcement not found", "imageUnavailable": "Image unavailable", "failedToLoadAnnouncements": "Failed to load announcements", "noAnnouncementsAvailable": "No announcements available", "checkBackLaterForUpdates": "Check back later for updates", "failedToLoadMoreAnnouncements": "Failed to load more announcements", "transactionActivity": "Transaction Activity", "totalTransactions": "Total Transactions", "totalValue": "Total Value", "monthlyAnalysis": "Monthly Analysis", "transactionStatus": "Transaction Status", "loadingTransactionData": "Loading transaction data...", "failedToLoadTransactionStats": "Failed to load transaction statistics", "unknownError": "An unknown error occurred", "noStatusDataAvailable": "No status data available", "viewTransactions": "View Transactions", "recentTransactions": "Recent Transactions", "viewAll": "View All", "failedToLoadTransactions": "Failed to load transactions", "retry": "Retry", "noTransactionsYet": "No transactions yet", "transactionsWillAppearHere": "Your recent transactions will appear here", "statusAccepted": "Accepted", "statusDelivered": "Delivered", "statusCancelled": "Cancelled", "myProduce": "My Produce", "all": "All", "available": "Available", "pending": "Pending", "expired": "Expired", "sold": "Sold", "addNewProduce": "Add New Produce", "addProduce": "Add Produce", "edit": "Edit", "delete": "Delete", "markAsExpired": "<PERSON> as Expired", "markAsSold": "<PERSON> as Sold", "markAsAvailable": "Mark as Available", "updateProduceStatus": "Updating status...", "produceStatusUpdated": "Produce status updated successfully", "failedToUpdateStatus": "Failed to update produce status", "deleteProduceTitle": "Delete Produce Listing", "deleteProduceConfirm": "Are you sure you want to delete this produce listing?", "deleting": "Deleting...", "produceDeletedSuccess": "Produce listing deleted successfully", "failedToDeleteProduce": "Failed to delete produce listing", "failedToLoadProduce": "Failed to load produce listings", "failedToLoadMoreProduce": "Failed to load more produce listings", "noProduceListings": "No produce listings yet", "addFirstProduce": "Add your first produce listing to start selling", "noFilteredProduceListings": "No {filter} produce listings", "@noFilteredProduceListings": {"placeholders": {"filter": {"type": "String"}}}, "changeFilterOrAdd": "Change the filter or add new produce", "showAllProduce": "Show All Produce", "availableFrom": "Available from ", "@availableFrom": {"placeholders": {"date": {"type": "String"}}}, "posted": "Posted: {date}", "@posted": {"placeholders": {"date": {"type": "String"}}}, "interestedCount": "{count} interested", "@interestedCount": {"placeholders": {"count": {"type": "int"}}}, "longPressToDebug": "Long press to debug", "tapToZoom": "Tap to zoom", "bulkUpdate": "Bulk Update", "bulkUpdateQuestion": "Update all expired produce listings?", "update": "Update", "failedToLoadImage": "Failed to load image", "editProduce": "Edit Produce", "selectCrop": "Please select a crop", "failedToUpdateProduce": "Failed to update produce", "failedToCreateProduce": "Failed to create produce", "crops": "Crops", "unit": "Unit", "availability": "Availability", "images": "Images", "updateProduce": "Update Produce", "createProduce": "Create Produce", "errorProcessingImage": "Error processing image. Please try again.", "addPrimaryImage": "Please add a primary image", "fulfillingRequirement": "Fulfilling Requirement", "produceDetails": "Produce Details", "availabilityDates": "Availability Dates", "productImages": "Product Images", "varietyOptional": "Variety (Optionals)", "quantity": "Quantity", "descriptionOptional": "Description (Optional)", "availableUntilOptional": "Available Until (Optional)", "harvestDateOptional": "Harvest Date (Optional)", "primaryImageRequired": "Primary Image (Required)", "secondaryImageOptional": "Secondary Image (Optional)", "tertiaryImageOptional": "Tertiary Image (Optional)", "varietyHint": "E.g., Basmati, Roma", "enterQuantity": "Enter quantity", "describeProduceHint": "Describe your produce qualities, origin, etc.", "quantityRequired": "Quantity is required", "enterValidNumber": "Please enter a valid number", "quantityGreaterThanZero": "Quantity must be greater than 0", "notSpecified": "Not specified", "transactionSummary": "Transaction Summary", "transactions": "Transactions", "pricePer": "Price per {unitType}", "@pricePer": {"placeholders": {"unitType": {"type": "String"}}}, "totalAmount": "Total Amount", "createProduceAndExpress": "Create Produce & Express Interest", "requiredTapToAddImage": "Required - Tap to add image", "primaryImageRequiredError": "Primary image is required", "whatHappensNext": "What happens next?", "afterSubmittingInfo": "After submitting, your produce will be created and automatically linked to this requirement. The FPO will be notified of your interest and will review your offer.", "produceCreatedSuccess": "Produce created and interest expressed successfully!"}