// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'FPO TRADERS';

  @override
  String get languageSelectionTitle => 'Select your Preferred language';

  @override
  String get english => 'English';

  @override
  String get tamil => 'தமிழ்';

  @override
  String get loginToYourAccount => 'Login to your account';

  @override
  String get username => 'Username';

  @override
  String get password => 'Password';

  @override
  String get loginNow => 'Login now';

  @override
  String welcomeUser(String name) {
    return 'Welcome! $name';
  }

  @override
  String get latestArticles => 'Latest articles';

  @override
  String get organicFarmingTechniques => 'Organic Farming Techniques';

  @override
  String get newCropVarieties => 'New Crop Varieties';

  @override
  String get marketPricesUpdate => 'Market Prices Update';

  @override
  String get sampleArticleTitle => 'Title of the Article Goes Here';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get home => 'Home';

  @override
  String get saved => 'Saved';

  @override
  String get help => 'Help';

  @override
  String get profile => 'Profile';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirmation => 'Logout Confirmation';

  @override
  String get logoutConfirmationMessage => 'Are you sure you want to log out?';

  @override
  String get cancel => 'Cancel';

  @override
  String get myQueries => 'My Queries';

  @override
  String get askQuestion => 'Ask a Question';

  @override
  String get noQueriesYet => 'No queries yet';

  @override
  String get createFirstQuery => 'Create your first query to get help from our experts';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get queries => 'Queries';

  @override
  String get knowledge => 'Knowledge';

  @override
  String get authTokenNotFound => 'Authentication token not found';

  @override
  String get failedToCreateQuery => 'Failed to create query';

  @override
  String get errorOccurred => 'Error occurred. Please try again.';

  @override
  String get retryLater => 'Please try again later';

  @override
  String get marketPrices => 'Market Prices';

  @override
  String get failedToLoadMarketPrices => 'Failed to load market prices';

  @override
  String get noMarketPricesAvailable => 'No market prices available';

  @override
  String get checkBackLater => 'Check back later for the latest prices';

  @override
  String get priceHistory => 'Price History';

  @override
  String get currentMarketPrice => 'Current Market Price';

  @override
  String get lastThirtyDaysPriceRange => 'Last 30 Days Price Range';

  @override
  String get minPrice => 'Min Price';

  @override
  String get avgPrice => 'Avg Price';

  @override
  String get maxPrice => 'Max Price';

  @override
  String get per => 'per';

  @override
  String get close => 'Close';

  @override
  String get queryTitle => 'Title';

  @override
  String get queryTitleHint => 'E.g., How to control pests in rice?';

  @override
  String get queryDescription => 'Description';

  @override
  String get queryDescriptionHint => 'Describe your issue in detail';

  @override
  String get addImage => 'Add Image';

  @override
  String get addImageOptional => 'Optional - Add an image related to your query';

  @override
  String get tapToAddImage => 'Tap to add an image';

  @override
  String get takePhoto => 'Take a photo';

  @override
  String get chooseFromGallery => 'Choose from gallery';

  @override
  String get submitQuery => 'Submit Query';

  @override
  String get queryCreatedSuccess => 'Query created successfully';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get queryDetails => 'Query Details';

  @override
  String get description => 'Description';

  @override
  String get responses => 'Responses';

  @override
  String get noResponses => 'No responses yet. Our experts will respond to your query soon.';

  @override
  String get closeQuery => 'Mark as Resolved';

  @override
  String get closeQueryTitle => 'Mark as Resolved';

  @override
  String get closeQueryMessage => 'Are you sure you want to mark this query as resolved?';

  @override
  String get queryClosedSuccess => 'Query marked as resolved successfully';

  @override
  String get queryNotFound => 'Query not found';

  @override
  String get statusOpen => 'Open';

  @override
  String get statusInProgress => 'In Progress';

  @override
  String get statusResolved => 'Resolved';

  @override
  String get statusClosed => 'Closed';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get contactInformation => 'Contact Information';

  @override
  String get addressInformation => 'Address Information';

  @override
  String get mobileNumber => 'Mobile Number';

  @override
  String get alternateNumber => 'Alternate Number';

  @override
  String get email => 'Email';

  @override
  String get addressLine1 => 'Address Line 1';

  @override
  String get addressLine2 => 'Address Line 2';

  @override
  String get locality => 'Locality';

  @override
  String get pincode => 'Pincode';

  @override
  String get invalidEmail => 'Please enter a valid email address';

  @override
  String get invalidPincode => 'Please enter a valid 6-digit pincode';

  @override
  String get save => 'Save';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get profilePictureUpdated => 'Profile picture updated successfully';

  @override
  String get announcement => 'Announcement';

  @override
  String get announcements => 'Announcements';

  @override
  String get errorLoadingAnnouncement => 'Error loading announcement';

  @override
  String get announcementNotFound => 'Announcement not found';

  @override
  String get imageUnavailable => 'Image unavailable';

  @override
  String get failedToLoadAnnouncements => 'Failed to load announcements';

  @override
  String get noAnnouncementsAvailable => 'No announcements available';

  @override
  String get checkBackLaterForUpdates => 'Check back later for updates';

  @override
  String get failedToLoadMoreAnnouncements => 'Failed to load more announcements';

  @override
  String get transactionActivity => 'Transaction Activity';

  @override
  String get totalTransactions => 'Total Transactions';

  @override
  String get totalValue => 'Total Value';

  @override
  String get monthlyAnalysis => 'Monthly Analysis';

  @override
  String get transactionStatus => 'Transaction Status';

  @override
  String get loadingTransactionData => 'Loading transaction data...';

  @override
  String get failedToLoadTransactionStats => 'Failed to load transaction statistics';

  @override
  String get unknownError => 'An unknown error occurred';

  @override
  String get noStatusDataAvailable => 'No status data available';

  @override
  String get viewTransactions => 'View Transactions';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get viewAll => 'View All';

  @override
  String get failedToLoadTransactions => 'Failed to load transactions';

  @override
  String get retry => 'Retry';

  @override
  String get noTransactionsYet => 'No transactions yet';

  @override
  String get transactionsWillAppearHere => 'Your recent transactions will appear here';

  @override
  String get statusAccepted => 'Accepted';

  @override
  String get statusDelivered => 'Delivered';

  @override
  String get statusCancelled => 'Cancelled';

  @override
  String get myProduce => 'My Produce';

  @override
  String get all => 'All';

  @override
  String get available => 'Available';

  @override
  String get pending => 'Pending';

  @override
  String get expired => 'Expired';

  @override
  String get sold => 'Sold';

  @override
  String get addNewProduce => 'Add New Produce';

  @override
  String get addProduce => 'Add Produce';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get markAsExpired => 'Mark as Expired';

  @override
  String get markAsSold => 'Mark as Sold';

  @override
  String get markAsAvailable => 'Mark as Available';

  @override
  String get updateProduceStatus => 'Updating status...';

  @override
  String get produceStatusUpdated => 'Produce status updated successfully';

  @override
  String get failedToUpdateStatus => 'Failed to update produce status';

  @override
  String get deleteProduceTitle => 'Delete Produce Listing';

  @override
  String get deleteProduceConfirm => 'Are you sure you want to delete this produce listing?';

  @override
  String get deleting => 'Deleting...';

  @override
  String get produceDeletedSuccess => 'Produce listing deleted successfully';

  @override
  String get failedToDeleteProduce => 'Failed to delete produce listing';

  @override
  String get failedToLoadProduce => 'Failed to load produce listings';

  @override
  String get failedToLoadMoreProduce => 'Failed to load more produce listings';

  @override
  String get noProduceListings => 'No produce listings yet';

  @override
  String get addFirstProduce => 'Add your first produce listing to start selling';

  @override
  String noFilteredProduceListings(String filter) {
    return 'No $filter produce listings';
  }

  @override
  String get changeFilterOrAdd => 'Change the filter or add new produce';

  @override
  String get showAllProduce => 'Show All Produce';

  @override
  String availableFrom(String date) {
    return 'Available from ';
  }

  @override
  String posted(String date) {
    return 'Posted: $date';
  }

  @override
  String interestedCount(int count) {
    return '$count interested';
  }

  @override
  String get longPressToDebug => 'Long press to debug';

  @override
  String get tapToZoom => 'Tap to zoom';

  @override
  String get bulkUpdate => 'Bulk Update';

  @override
  String get bulkUpdateQuestion => 'Update all expired produce listings?';

  @override
  String get update => 'Update';

  @override
  String get failedToLoadImage => 'Failed to load image';

  @override
  String get editProduce => 'Edit Produce';

  @override
  String get selectCrop => 'Please select a crop';

  @override
  String get failedToUpdateProduce => 'Failed to update produce';

  @override
  String get failedToCreateProduce => 'Failed to create produce';

  @override
  String get crops => 'Crops';

  @override
  String get unit => 'Unit';

  @override
  String get availability => 'Availability';

  @override
  String get images => 'Images';

  @override
  String get updateProduce => 'Update Produce';

  @override
  String get createProduce => 'Create Produce';

  @override
  String get errorProcessingImage => 'Error processing image. Please try again.';

  @override
  String get addPrimaryImage => 'Please add a primary image';

  @override
  String get fulfillingRequirement => 'Fulfilling Requirement';

  @override
  String get produceDetails => 'Produce Details';

  @override
  String get availabilityDates => 'Availability Dates';

  @override
  String get productImages => 'Product Images';

  @override
  String get varietyOptional => 'Variety (Optionals)';

  @override
  String get quantity => 'Quantity';

  @override
  String get descriptionOptional => 'Description (Optional)';

  @override
  String get availableUntilOptional => 'Available Until (Optional)';

  @override
  String get harvestDateOptional => 'Harvest Date (Optional)';

  @override
  String get primaryImageRequired => 'Primary Image (Required)';

  @override
  String get secondaryImageOptional => 'Secondary Image (Optional)';

  @override
  String get tertiaryImageOptional => 'Tertiary Image (Optional)';

  @override
  String get varietyHint => 'E.g., Basmati, Roma';

  @override
  String get enterQuantity => 'Enter quantity';

  @override
  String get describeProduceHint => 'Describe your produce qualities, origin, etc.';

  @override
  String get quantityRequired => 'Quantity is required';

  @override
  String get enterValidNumber => 'Please enter a valid number';

  @override
  String get quantityGreaterThanZero => 'Quantity must be greater than 0';

  @override
  String get notSpecified => 'Not specified';

  @override
  String get transactionSummary => 'Transaction Summary';

  @override
  String get transactions => 'Transactions';

  @override
  String pricePer(String unitType) {
    return 'Price per $unitType';
  }

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get createProduceAndExpress => 'Create Produce & Express Interest';

  @override
  String get requiredTapToAddImage => 'Required - Tap to add image';

  @override
  String get primaryImageRequiredError => 'Primary image is required';

  @override
  String get whatHappensNext => 'What happens next?';

  @override
  String get afterSubmittingInfo => 'After submitting, your produce will be created and automatically linked to this requirement. The FPO will be notified of your interest and will review your offer.';

  @override
  String get produceCreatedSuccess => 'Produce created and interest expressed successfully!';
}
