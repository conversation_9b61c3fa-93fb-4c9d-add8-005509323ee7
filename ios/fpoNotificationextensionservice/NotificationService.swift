import UserNotifications
import FirebaseMessaging

class NotificationService: UNNotificationServiceExtension {

    override func didReceive(
        _ request: UNNotificationRequest,
        withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void
    ) {
        // This line is the magic: it downloads and adds the image from the push payload
        Messaging.serviceExtension().populateNotificationContent(
            for: request,
            withContentHandler: contentHandler
        )
    }

    override func serviceExtensionTimeWillExpire() {
        // Firebase handles this internally — you don't need to modify this unless you're doing custom fallback
    }
}