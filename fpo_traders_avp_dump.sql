-- MySQL dump 10.13  Distrib 9.2.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: fpo_traders_avp
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `announcements`
--

DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcements` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `image_url` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `announcements_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `announcements_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcements`
--

LOCK TABLES `announcements` WRITE;
/*!40000 ALTER TABLE `announcements` DISABLE KEYS */;
INSERT INTO `announcements` VALUES (2,'Drumstick price increases','This is the bring to all your notice that the price of drumsticks will increase by 30% from May 2025. ','/uploads/articles/image-1744955489289-353640645.jpg',1,1,'2025-04-18 05:41:49','2025-04-18 05:51:29'),(3,'Weather Update','Theres a cyclone forming in the north end of our state, we can expect medium rains to very Heavy Rains. ','/uploads/articles/image-1744960112458-423851064.jpg',1,NULL,'2025-04-18 07:08:32','2025-04-18 07:08:32'),(4,'Gokul announcement','this is the description for the announcement','/uploads/articles/image-1744991413337-716992437.jpg',1,NULL,'2025-04-18 15:50:13','2025-04-18 15:50:13');
/*!40000 ALTER TABLE `announcements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `farmer_crops`
--

DROP TABLE IF EXISTS `farmer_crops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `farmer_crops` (
  `farmer_id` int DEFAULT NULL,
  `crop_id` int DEFAULT NULL,
  KEY `farmer_id` (`farmer_id`),
  KEY `crop_id` (`crop_id`),
  CONSTRAINT `farmer_crops_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `farmer_crops_ibfk_2` FOREIGN KEY (`crop_id`) REFERENCES `master_crops` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `farmer_crops`
--

LOCK TABLES `farmer_crops` WRITE;
/*!40000 ALTER TABLE `farmer_crops` DISABLE KEYS */;
INSERT INTO `farmer_crops` VALUES (5,1),(11,3),(11,1),(24,5),(24,4),(24,3),(24,2),(24,1),(25,4),(25,3),(26,5),(26,4),(26,2),(26,3),(26,6),(26,1);
/*!40000 ALTER TABLE `farmer_crops` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `farmer_equipment`
--

DROP TABLE IF EXISTS `farmer_equipment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `farmer_equipment` (
  `farmer_id` int DEFAULT NULL,
  `equipment_type_id` int DEFAULT NULL,
  KEY `farmer_id` (`farmer_id`),
  KEY `equipment_type_id` (`equipment_type_id`),
  CONSTRAINT `farmer_equipment_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `farmer_equipment_ibfk_2` FOREIGN KEY (`equipment_type_id`) REFERENCES `master_equipment_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `farmer_equipment`
--

LOCK TABLES `farmer_equipment` WRITE;
/*!40000 ALTER TABLE `farmer_equipment` DISABLE KEYS */;
INSERT INTO `farmer_equipment` VALUES (5,1),(5,2),(24,2),(24,3),(24,1),(25,2),(25,3),(26,2),(26,3),(26,1);
/*!40000 ALTER TABLE `farmer_equipment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `farmer_livestock`
--

DROP TABLE IF EXISTS `farmer_livestock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `farmer_livestock` (
  `farmer_id` int DEFAULT NULL,
  `livestock_type_id` int DEFAULT NULL,
  KEY `farmer_id` (`farmer_id`),
  KEY `livestock_type_id` (`livestock_type_id`),
  CONSTRAINT `farmer_livestock_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `farmer_livestock_ibfk_2` FOREIGN KEY (`livestock_type_id`) REFERENCES `master_livestock_types` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `farmer_livestock`
--

LOCK TABLES `farmer_livestock` WRITE;
/*!40000 ALTER TABLE `farmer_livestock` DISABLE KEYS */;
INSERT INTO `farmer_livestock` VALUES (5,1),(5,2),(24,1),(24,2),(24,4),(25,2),(25,4),(26,2),(26,4);
/*!40000 ALTER TABLE `farmer_livestock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `farmer_produce`
--

DROP TABLE IF EXISTS `farmer_produce`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `farmer_produce` (
  `id` int NOT NULL AUTO_INCREMENT,
  `farmer_id` int NOT NULL,
  `product_id` int NOT NULL,
  `variety` varchar(100) DEFAULT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit_type` varchar(20) NOT NULL,
  `primary_image_url` varchar(255) NOT NULL,
  `secondary_image_url` varchar(255) DEFAULT NULL,
  `tertiary_image_url` varchar(255) DEFAULT NULL,
  `description` text,
  `harvest_date` date DEFAULT NULL,
  `available_from` date NOT NULL,
  `available_until` date DEFAULT NULL,
  `status` enum('available','pending','sold','expired') DEFAULT 'available',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_farmer_produce_farmer` (`farmer_id`),
  KEY `idx_farmer_produce_product` (`product_id`),
  KEY `idx_farmer_produce_status` (`status`),
  KEY `idx_farmer_produce_dates` (`available_from`,`available_until`),
  CONSTRAINT `farmer_produce_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `farmer_produce_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `master_crops` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `farmer_produce`
--

LOCK TABLES `farmer_produce` WRITE;
/*!40000 ALTER TABLE `farmer_produce` DISABLE KEYS */;
INSERT INTO `farmer_produce` VALUES (2,23,4,NULL,150.00,'kg','/uploads/produce-images/mobile-produce-1743361311313-114586345.jpg',NULL,NULL,'test','2025-02-27','2025-03-31',NULL,'sold','2025-03-30 19:01:51','2025-04-18 07:17:12'),(4,23,5,'Imported variety',100.00,'kg','/uploads/produce-images/mobile-produce-1744541239077-83565397.jpg',NULL,NULL,'this is the description','2025-03-28','2025-04-13',NULL,'available','2025-04-13 10:47:19','2025-04-16 16:34:02'),(8,23,1,'PBW-343',750.00,'kg','/uploads/produce-images/produce-1744562828724-658686176.jpg',NULL,NULL,NULL,'2025-04-14','2025-04-13','2025-04-22','expired','2025-04-13 16:47:08','2025-04-17 16:46:10'),(9,23,4,'Poompalam',200.00,'kg','/uploads/produce-images/mobile-produce-1744565275643-993105051.jpg',NULL,NULL,NULL,'2025-03-12','2025-04-13','2025-05-14','expired','2025-04-13 17:27:55','2025-04-17 16:46:07'),(10,24,5,'Imported',20.00,'kg','/uploads/produce-images/produce-1744651561827-580299337.jpg',NULL,NULL,NULL,'2025-04-15','2025-04-14','2025-05-15','sold','2025-04-14 17:26:01','2025-04-16 16:33:50'),(11,24,4,'golden',20.00,'kg','/uploads/produce-images/produce-1744906386144-959294919.jpg',NULL,NULL,NULL,'2025-04-16','2025-04-17','2025-05-22','pending','2025-04-17 16:13:06','2025-04-17 16:13:06'),(12,25,4,'Red',100.00,'kg','/uploads/produce-images/mobile-produce-1744946116270-51983881.jpg',NULL,NULL,'this is the desc','2025-04-18','2025-04-18','2025-04-23','available','2025-04-18 03:15:16','2025-04-18 03:21:47'),(13,23,4,'yellow',50.00,'kg','/uploads/produce-images/mobile-produce-1744960361358-123001811.jpg',NULL,NULL,'this is test','2025-04-17','2025-04-18','2025-04-23','pending','2025-04-18 07:12:41','2025-04-21 10:32:59'),(14,23,2,'green',35.00,'kg','/uploads/produce-images/mobile-produce-1744972390137-189375855.jpg',NULL,NULL,'hello','2025-04-16','2025-04-18','2025-04-25','available','2025-04-18 10:33:10','2025-04-18 10:33:10'),(15,23,6,'none',100.00,'kg','/uploads/produce-images/produce-1744992144447-455993775.jpg',NULL,NULL,NULL,'2025-04-16','2025-04-18','2025-04-22','sold','2025-04-18 16:02:24','2025-04-18 16:04:19'),(16,24,1,'234',30.00,'kg','/uploads/produce-images/mobile-produce-1745229117804-995803599.jpg',NULL,NULL,'available','2025-04-18','2025-04-21','2025-04-26','available','2025-04-21 09:51:57','2025-04-21 09:51:57'),(17,24,6,'country',100.00,'kg','/uploads/produce-images/produce-1745229281572-93294180.jpg',NULL,NULL,'desc','2025-04-17','2025-04-21','2025-04-29','sold','2025-04-21 09:54:41','2025-04-21 09:56:28');
/*!40000 ALTER TABLE `farmer_produce` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `farmers`
--

DROP TABLE IF EXISTS `farmers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `farmers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `full_name` varchar(100) NOT NULL,
  `mobile_number` varchar(15) DEFAULT NULL,
  `alternate_number` varchar(15) DEFAULT NULL,
  `email_id` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` varchar(20) DEFAULT NULL,
  `address_line1` text,
  `address_line2` text,
  `locality` varchar(100) DEFAULT NULL,
  `city_id` int DEFAULT NULL,
  `state_id` int DEFAULT NULL,
  `pincode` varchar(10) DEFAULT NULL,
  `farm_size` decimal(10,2) DEFAULT NULL,
  `crops_grown` text,
  `livestock_owned` text,
  `farming_experience` int DEFAULT NULL,
  `tools_equipment` text,
  `aadhaar_number` varchar(12) DEFAULT NULL,
  `ration_card_number` varchar(50) DEFAULT NULL,
  `farmer_id` varchar(50) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `id_proof_url` varchar(255) DEFAULT NULL,
  `profile_pic_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `farming_type_id` int DEFAULT NULL,
  `irrigation_type_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mobile_number` (`mobile_number`),
  UNIQUE KEY `aadhaar_number` (`aadhaar_number`),
  UNIQUE KEY `username` (`username`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `farming_type_id` (`farming_type_id`),
  KEY `irrigation_type_id` (`irrigation_type_id`),
  KEY `city_id` (`city_id`),
  KEY `state_id` (`state_id`),
  CONSTRAINT `farmers_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `farmers_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`),
  CONSTRAINT `farmers_ibfk_4` FOREIGN KEY (`farming_type_id`) REFERENCES `master_farming_types` (`id`),
  CONSTRAINT `farmers_ibfk_5` FOREIGN KEY (`irrigation_type_id`) REFERENCES `master_irrigation_types` (`id`),
  CONSTRAINT `farmers_ibfk_6` FOREIGN KEY (`city_id`) REFERENCES `master_cities` (`id`),
  CONSTRAINT `farmers_ibfk_7` FOREIGN KEY (`state_id`) REFERENCES `master_states` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `farmers`
--

LOCK TABLES `farmers` WRITE;
/*!40000 ALTER TABLE `farmers` DISABLE KEYS */;
INSERT INTO `farmers` VALUES (5,'John Doe','9876543213','9876543211','<EMAIL>','1990-01-01','male','123 Farm Road',NULL,NULL,1,1,'123456',50.50,NULL,NULL,5,NULL,'123456789012',NULL,NULL,NULL,NULL,NULL,NULL,'2025-01-26 06:46:36','2025-01-26 06:46:36',1,NULL,1,1),(8,'Rajesh Kumars','9876543212','9876543211','<EMAIL>','1985-06-13','male','123 Farm Road','Near Water Tank','Nashik Rural',1,1,'422003',5.50,NULL,NULL,10,NULL,'123456783012','RAT123456','FRM001','rajesh.farmer','$2a$10$FzXvfM7ru.9xkPDT5VuKUeH1RzZLpluDZZMg8kw080EPR/xetvZum',NULL,NULL,'2025-02-02 08:15:21','2025-02-23 17:42:26',1,1,2,1),(9,'Test Farmer','9876543210','9876543211','<EMAIL>','1990-01-01','male','123 Test Street','Test Area','Test Locality',1,1,'400001',5.50,NULL,NULL,5,NULL,NULL,NULL,NULL,'testfarmer','$2a$10$Ay/M8bAuV8J2BpwbcLZSg.v5s/wc82yP5HTjdXeieMdYgvX9R5Wda',NULL,NULL,'2025-02-21 17:58:36','2025-02-21 17:58:36',1,NULL,1,1),(10,'gokul Prasad c','9843757161','8787887872','<EMAIL>','1987-01-11','male','lineone','line two','locality',1,2,'641604',2.00,NULL,NULL,3,NULL,'767611156245','7162563','1672563','gokulprasad','Kalso@1487',NULL,NULL,'2025-02-23 17:08:43','2025-02-23 17:52:16',1,1,1,2),(11,'Raju Mohan','9822298222','1288812888','<EMAIL>','1992-04-06','male','line one','line two','locality',1,2,'641604',2.00,NULL,NULL,2,NULL,'768723982378','1298376','8765465','rajumohan','654321',NULL,NULL,'2025-02-24 02:53:38','2025-02-24 03:40:32',1,1,1,2),(12,'test name','7743737837','1782783784','<EMAIL>','1987-01-09','male','address line one','address line two','locality',1,2,'641604',3.00,NULL,NULL,3,NULL,'189798768765','17867577','1676761','testname','654321',NULL,NULL,'2025-02-24 03:42:30','2025-02-24 03:47:15',1,1,1,2),(13,'Raja Velmurugan','7367635645','1562453452','<EMAIL>','1992-04-08','male','address line one','address line two','locality',1,2,'641604',4.00,NULL,NULL,5,NULL,'878711267526','1287676','1278617','rajavel','654321','/uploads/documents/id_proof-1740369735236-40181636.jpg','/uploads/profiles/profile_pic-1740369735242-502506150.jpg','2025-02-24 04:02:15','2025-02-24 04:02:15',1,NULL,2,2),(14,'velmurugan','9898978787','7897897897','<EMAIL>','1992-07-01','male','line one','line two','locality',1,2,'641604',5.00,NULL,NULL,7,NULL,'123564651452','12761276','12763878','velmurugan','654321','/uploads/documents/id_proof-1740372637170-515816115.jpg','/uploads/profiles/profile_pic-1740372637177-92628180.jpg','2025-02-24 04:50:37','2025-02-24 04:50:37',1,NULL,2,1),(23,'Selvakumar','9811198222','1288818999','<EMAIL>','2025-02-21','male','line one','line two','locality',1,2,'641604',3.00,'[3,2]','[1,2]',4,'[3,1]','761761761267','76716','16725',NULL,'$2a$10$AX3AIKwzNwBeTn0bgBpCdeahiZ57bSBs7YJUjAmwQpI8wCZUHUL/K',NULL,NULL,'2025-02-27 11:39:09','2025-04-18 16:00:39',1,1,1,2),(24,'selvakumar2','9833321222','9992212281','<EMAIL>','1994-07-13','male','Address line one','Address line two','locality',1,2,'641605',10.00,'[5,4,3,2,1]','[1,2,4]',20,'[2,3,1]','128798798799','234234234234','123111','selvakumar2','$2b$10$5e30b6.2dTbAwcK9TFNx9eeO5YRIUAjlXIKU2L5Fw7WzumKQvTG0S',NULL,NULL,'2025-04-13 08:29:57','2025-04-26 10:05:05',1,NULL,2,1),(25,'Rajamani','9442003498','9843997161','<EMAIL>','1967-04-03','male','Addressline one','addressl line two','Nilali',1,2,'641605',3.00,'[4,3]','[2,4]',12,'[2,3]','129812981298','1298999','12999989','rajamani','$2a$10$JS7KiS.M/VK2FWfglTWC3OoeKQ9DWnuRfAV9llKKeAZcNLGdWrNlW',NULL,NULL,'2025-04-18 02:54:39','2025-04-18 02:54:39',1,NULL,2,1),(26,'selvakumar3','9898198982','9898398984','<EMAIL>','1993-02-11','male','address line one','address line two','Avinashi',1,2,'641604',12.00,'[5,4,2,3,6,1]','[2,4]',8,'[2,3,1]','128738716872','123879','1872871','selvakumar3','$2a$10$UwJxsNy4B0O09nDULPJx6OiOkVmCpp//PVQfUvtlmDiYmI/PSktJ6',NULL,NULL,'2025-04-26 10:14:05','2025-04-26 10:14:05',1,NULL,2,2);
/*!40000 ALTER TABLE `farmers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fcm_tokens`
--

DROP TABLE IF EXISTS `fcm_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fcm_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `entity_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `device_type` varchar(20) DEFAULT NULL,
  `device_name` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `entity_type` enum('user','farmer') NOT NULL DEFAULT 'user',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_token` (`entity_id`,`token`),
  KEY `idx_token` (`token`),
  KEY `idx_user_id` (`entity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fcm_tokens`
--

LOCK TABLES `fcm_tokens` WRITE;
/*!40000 ALTER TABLE `fcm_tokens` DISABLE KEYS */;
INSERT INTO `fcm_tokens` VALUES (6,26,'fTUJa0GphU3UsyHwh1KB9u:APA91bFrrwbICpQyyBWB_bPzJonOexl2oaXBeTslCpy1IRRnqUjv80AmNKa_00MsUWdsWnMmoHg_IM1ds73ueasd_lZnDDt1tyYWPZucrZv55AZgRBR9KO0','ios','iOS Device','2025-04-26 10:52:04','2025-04-26 10:55:43',0,'farmer'),(7,26,'cMwD67vtqEZ7rzVs_-MA8w:APA91bFtWFp0HyoKWMBnPHzODg3GnWvXwT7gjA7qz2iNgwG32A5F9k0UjkIZHObgjA4p0r-ltmp-gdnwb-bma5ACndkQt-8JPBneTn0nAET_uaTsffGU5mA','ios','iOS Device','2025-04-26 10:55:43','2025-04-26 10:58:38',1,'farmer');
/*!40000 ALTER TABLE `fcm_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fpo_inventory`
--

DROP TABLE IF EXISTS `fpo_inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fpo_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variety` varchar(100) DEFAULT NULL,
  `quantity_in_stock` decimal(10,2) NOT NULL DEFAULT '0.00',
  `unit_type` varchar(20) NOT NULL,
  `last_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_fpo_inventory_product` (`product_id`),
  CONSTRAINT `fpo_inventory_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `master_crops` (`id`),
  CONSTRAINT `fpo_inventory_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fpo_inventory_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fpo_inventory`
--

LOCK TABLES `fpo_inventory` WRITE;
/*!40000 ALTER TABLE `fpo_inventory` DISABLE KEYS */;
/*!40000 ALTER TABLE `fpo_inventory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fpo_requirements`
--

DROP TABLE IF EXISTS `fpo_requirements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fpo_requirements` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variety` varchar(100) DEFAULT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit_type` varchar(20) NOT NULL,
  `price_offered` decimal(10,2) NOT NULL,
  `status` enum('active','fulfilled','expired','cancelled') DEFAULT 'active',
  `description` text,
  `required_by` date DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `market_price_at_creation` decimal(10,2) DEFAULT NULL,
  `market_price_unit` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_fpo_requirements_product` (`product_id`),
  KEY `idx_fpo_requirements_status` (`status`),
  CONSTRAINT `fpo_requirements_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `master_crops` (`id`),
  CONSTRAINT `fpo_requirements_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fpo_requirements_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fpo_requirements`
--

LOCK TABLES `fpo_requirements` WRITE;
/*!40000 ALTER TABLE `fpo_requirements` DISABLE KEYS */;
INSERT INTO `fpo_requirements` VALUES (1,1,'HD-2967',500.00,'kg',25.50,'fulfilled','Looking for high-quality wheat for flour production','2025-04-30',1,1,'2025-03-23 08:24:42','2025-03-30 06:58:14',42.00,'kg'),(2,1,'PBW-343',750.00,'kg',100.00,'active','Wheat required for bakery products','2025-04-15',1,1,'2025-03-23 08:24:42','2025-04-17 12:29:36',42.00,'kg'),(5,3,'Jasmine',800.00,'kg',58.50,'expired','Jasmine rice for restaurant supply','2025-03-01',1,1,'2025-02-24 04:45:00','2025-04-18 03:34:26',99.50,'kg'),(6,3,'Brown',600.00,'kg',45.25,'expired','Organic brown rice for health stores','2025-02-28',1,NULL,'2025-02-20 04:00:00','2025-03-23 08:24:42',NULL,NULL),(7,4,'Cavendish',300.00,'kg',42.00,'fulfilled','Fresh bananas for supermarket chain','2025-03-10',1,1,'2025-03-23 08:24:42','2025-03-23 08:42:38',NULL,NULL),(8,4,'Red banana',150.00,'kg',55.00,'cancelled','Special variety red bananas order cancelled','2025-03-05',1,NULL,'2025-02-26 08:50:00','2025-03-23 08:24:42',NULL,NULL),(9,5,'Imported',20.00,'kg',95.00,'fulfilled',NULL,'2025-04-16',1,1,'2025-03-30 06:08:31','2025-04-16 17:30:53',100.00,'kg'),(10,4,'golden',20.00,'kg',120.00,'fulfilled',NULL,'2025-04-24',1,1,'2025-04-17 16:06:33','2025-04-18 10:29:15',100.00,'kg'),(11,6,'none',100.00,'kg',125.00,'active',NULL,'2025-04-29',1,NULL,'2025-04-18 15:59:32','2025-04-18 15:59:32',120.00,'kg'),(12,4,'Yellow',20.00,'kg',125.00,'fulfilled',NULL,NULL,1,1,'2025-04-21 09:49:27','2025-04-21 10:32:59',120.00,'kg');
/*!40000 ALTER TABLE `fpo_requirements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_transactions`
--

DROP TABLE IF EXISTS `inventory_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `inventory_id` int NOT NULL,
  `transaction_id` int DEFAULT NULL,
  `transaction_type` enum('in','out','adjustment') NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `notes` text,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_inventory_transactions_inventory` (`inventory_id`),
  KEY `idx_inventory_transactions_transaction` (`transaction_id`),
  CONSTRAINT `inventory_transactions_ibfk_1` FOREIGN KEY (`inventory_id`) REFERENCES `fpo_inventory` (`id`),
  CONSTRAINT `inventory_transactions_ibfk_2` FOREIGN KEY (`transaction_id`) REFERENCES `marketplace_transactions` (`id`),
  CONSTRAINT `inventory_transactions_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `inventory_transactions_ibfk_4` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_transactions`
--

LOCK TABLES `inventory_transactions` WRITE;
/*!40000 ALTER TABLE `inventory_transactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_article_likes`
--

DROP TABLE IF EXISTS `knowledge_article_likes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_article_likes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `article_id` int NOT NULL,
  `user_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `article_user_unique` (`article_id`,`user_id`),
  CONSTRAINT `knowledge_article_likes_ibfk_1` FOREIGN KEY (`article_id`) REFERENCES `knowledge_articles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_article_likes`
--

LOCK TABLES `knowledge_article_likes` WRITE;
/*!40000 ALTER TABLE `knowledge_article_likes` DISABLE KEYS */;
/*!40000 ALTER TABLE `knowledge_article_likes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_article_views`
--

DROP TABLE IF EXISTS `knowledge_article_views`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_article_views` (
  `id` int NOT NULL AUTO_INCREMENT,
  `article_id` int NOT NULL,
  `user_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `article_id` (`article_id`),
  CONSTRAINT `knowledge_article_views_ibfk_1` FOREIGN KEY (`article_id`) REFERENCES `knowledge_articles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_article_views`
--

LOCK TABLES `knowledge_article_views` WRITE;
/*!40000 ALTER TABLE `knowledge_article_views` DISABLE KEYS */;
/*!40000 ALTER TABLE `knowledge_article_views` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_articles`
--

DROP TABLE IF EXISTS `knowledge_articles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_articles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `knowledge_articles_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `knowledge_articles_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_articles`
--

LOCK TABLES `knowledge_articles` WRITE;
/*!40000 ALTER TABLE `knowledge_articles` DISABLE KEYS */;
INSERT INTO `knowledge_articles` VALUES (2,'Pests of india','Vestibulum a finibus risus. Nunc sed velit eget lorem sagittis semper. Donec cursus enim et lacus venenatis, ac consectetur turpis laoreet. Quisque nisl metus, commodo id laoreet in, semper sit amet mi. Donec tincidunt urna eget congue fringilla. Suspendisse potenti. Sed in lorem dolor.','/uploads/articles/image-1740462236557-633765440.jpeg',1,NULL,'2025-02-25 05:43:56','2025-02-25 05:43:56'),(3,'this is new title','This is the description','/uploads/articles/image-1740485989833-692379994.jpg',1,NULL,'2025-02-25 12:19:49','2025-02-25 12:19:49'),(5,'title of the article','lskfjlskdfj\r\nsdlfkjslfj\r\nsdflkjsldfjs\r\nsldkfjlskjflsjdflksdjfs\r\nlsdkfjlsdkjfsjf\r\nsldkfjlskdfjlsjdf\r\nsldkfjlskdjf','/uploads/articles/image-1744991736173-101720296.jpg',1,NULL,'2025-04-18 15:55:36','2025-04-18 15:55:36');
/*!40000 ALTER TABLE `knowledge_articles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `knowledge_hub`
--

DROP TABLE IF EXISTS `knowledge_hub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `knowledge_hub` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `image_url` varchar(255) DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `knowledge_hub_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `knowledge_hub_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `knowledge_hub`
--

LOCK TABLES `knowledge_hub` WRITE;
/*!40000 ALTER TABLE `knowledge_hub` DISABLE KEYS */;
/*!40000 ALTER TABLE `knowledge_hub` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `market_prices`
--

DROP TABLE IF EXISTS `market_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `market_prices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `price_per_unit` decimal(10,2) NOT NULL,
  `unit_type` varchar(20) NOT NULL,
  `price_date` date NOT NULL,
  `notes` text,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_market_prices_product` (`product_id`),
  KEY `idx_market_prices_date` (`price_date`),
  CONSTRAINT `market_prices_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `master_crops` (`id`),
  CONSTRAINT `market_prices_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `market_prices_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `market_prices`
--

LOCK TABLES `market_prices` WRITE;
/*!40000 ALTER TABLE `market_prices` DISABLE KEYS */;
INSERT INTO `market_prices` VALUES (16,1,35.50,'kg','2023-12-01','Wheat price stable due to good supply',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(17,2,65.75,'kg','2023-12-01','Test crop premium quality, high demand',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(18,3,95.00,'kg','2023-12-01','Rice limited supply',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(19,4,42.00,'kg','2023-12-01','Banana fresh arrival',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(20,1,38.25,'kg','2023-12-08','Wheat slight increase due to demand',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(21,2,63.50,'kg','2023-12-08','Test crop price decreased slightly',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(22,3,97.80,'kg','2023-12-08','Rice premium quality',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(23,4,40.50,'kg','2023-12-08','Banana good harvest',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(24,1,42.00,'kg','2023-12-15','Wheat price increased due to season',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(25,2,60.25,'kg','2023-12-15','Test crop good harvest',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(26,3,99.50,'kg','2023-12-15','Rice high quality',1,NULL,'2025-03-22 19:06:35','2025-03-22 19:06:35'),(30,5,50.00,'kg','2025-03-30','notes',1,1,'2025-03-30 05:50:20','2025-03-30 09:04:24'),(31,4,20.00,'kg','2025-03-30','banana price',1,NULL,'2025-03-30 09:13:07','2025-03-30 09:13:07'),(32,5,60.00,'kg','2025-03-31',NULL,1,NULL,'2025-03-31 02:42:02','2025-03-31 02:42:02'),(33,5,100.00,'kg','2025-04-04',NULL,1,NULL,'2025-04-04 10:05:39','2025-04-04 10:05:39'),(34,4,100.00,'kg','2025-04-07','test',1,NULL,'2025-04-07 00:14:51','2025-04-07 00:14:51'),(35,5,70.00,'kg','2025-04-17','todays price',1,NULL,'2025-04-17 17:34:46','2025-04-17 17:34:46'),(36,1,123.00,'kg','2025-04-17','Wheat market price',1,NULL,'2025-04-17 17:51:17','2025-04-17 17:51:17'),(37,5,120.00,'kg','2025-04-18','notes for red',1,NULL,'2025-04-18 15:51:29','2025-04-18 15:51:29'),(38,6,120.00,'kg','2025-04-18',NULL,1,NULL,'2025-04-18 15:58:46','2025-04-18 15:58:46'),(40,4,120.00,'kg','2025-04-21','notes',1,NULL,'2025-04-21 09:48:31','2025-04-21 09:48:31');
/*!40000 ALTER TABLE `market_prices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `marketplace_transactions`
--

DROP TABLE IF EXISTS `marketplace_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `marketplace_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_type` enum('requirement_based','direct_purchase') NOT NULL,
  `farmer_id` int NOT NULL,
  `fpo_requirement_id` int DEFAULT NULL,
  `farmer_produce_id` int NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `unit_type` varchar(20) NOT NULL,
  `price_per_unit` decimal(10,2) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('accepted','in_progress','delivered','closed','cancelled') DEFAULT 'accepted',
  `status_updated_at` timestamp NULL DEFAULT NULL,
  `notes` text,
  `created_by` int NOT NULL COMMENT 'ID of creator, can be user_id or farmer_id',
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_marketplace_transactions_farmer` (`farmer_id`),
  KEY `idx_marketplace_transactions_requirement` (`fpo_requirement_id`),
  KEY `idx_marketplace_transactions_produce` (`farmer_produce_id`),
  KEY `idx_marketplace_transactions_status` (`status`),
  CONSTRAINT `marketplace_transactions_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `marketplace_transactions_ibfk_2` FOREIGN KEY (`fpo_requirement_id`) REFERENCES `fpo_requirements` (`id`),
  CONSTRAINT `marketplace_transactions_ibfk_3` FOREIGN KEY (`farmer_produce_id`) REFERENCES `farmer_produce` (`id`),
  CONSTRAINT `marketplace_transactions_ibfk_5` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `marketplace_transactions`
--

LOCK TABLES `marketplace_transactions` WRITE;
/*!40000 ALTER TABLE `marketplace_transactions` DISABLE KEYS */;
INSERT INTO `marketplace_transactions` VALUES (2,'requirement_based',23,2,8,750.00,'kg',24.75,18562.50,'accepted','2025-04-13 16:47:08','Created for requirement #2',23,NULL,'2025-04-13 16:47:08','2025-04-13 16:47:08'),(3,'requirement_based',23,9,4,20.00,'kg',95.00,1900.00,'cancelled','2025-04-16 16:34:02','Interest for requirement #9',23,1,'2025-04-13 17:41:55','2025-04-16 16:34:02'),(4,'requirement_based',24,9,10,20.00,'kg',95.00,1900.00,'delivered','2025-04-16 16:33:50','Created for requirement #9',24,1,'2025-04-14 17:26:01','2025-04-16 16:33:50'),(5,'requirement_based',23,9,9,20.00,'kg',95.00,1900.00,'in_progress','2025-04-18 10:25:32','Admin-created connection between produce and requirement',1,1,'2025-04-16 17:30:53','2025-04-18 10:25:32'),(6,'requirement_based',23,10,2,20.00,'kg',120.00,2400.00,'delivered','2025-04-18 07:17:12','Linked from matching produce dialog',1,1,'2025-04-17 16:07:08','2025-04-18 07:17:12'),(7,'requirement_based',24,10,11,20.00,'kg',120.00,2400.00,'accepted','2025-04-17 16:13:06','Created for requirement #10',24,NULL,'2025-04-17 16:13:06','2025-04-17 16:13:06'),(8,'requirement_based',25,10,12,100.00,'kg',120.00,12000.00,'cancelled','2025-04-18 03:21:47','Admin-created transaction for requirement #10',1,1,'2025-04-18 03:16:10','2025-04-18 03:21:47'),(9,'requirement_based',23,11,15,100.00,'kg',125.00,12500.00,'delivered','2025-04-18 16:04:19','Created for requirement #11',23,1,'2025-04-18 16:02:24','2025-04-18 16:04:19'),(10,'requirement_based',24,11,17,100.00,'kg',125.00,12500.00,'delivered','2025-04-21 09:56:28','Created for requirement #11',24,1,'2025-04-21 09:54:41','2025-04-21 09:56:28'),(11,'requirement_based',23,12,13,20.00,'kg',125.00,2500.00,'accepted','2025-04-21 10:32:59','Linked from matching produce dialog',1,NULL,'2025-04-21 10:32:59','2025-04-21 10:32:59');
/*!40000 ALTER TABLE `marketplace_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_cities`
--

DROP TABLE IF EXISTS `master_cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_cities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state_id` int NOT NULL,
  `city_name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `state_id` (`state_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_cities_ibfk_1` FOREIGN KEY (`state_id`) REFERENCES `master_states` (`id`),
  CONSTRAINT `master_cities_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_cities_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_cities`
--

LOCK TABLES `master_cities` WRITE;
/*!40000 ALTER TABLE `master_cities` DISABLE KEYS */;
INSERT INTO `master_cities` VALUES (1,2,'Tiruppur',1,1,NULL,'2024-12-15 07:51:31','2024-12-15 07:51:31'),(2,2,'Avinashi',1,1,1,'2025-02-22 16:25:29','2025-02-22 16:34:26');
/*!40000 ALTER TABLE `master_cities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_crop_categories`
--

DROP TABLE IF EXISTS `master_crop_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_crop_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `category_name` (`category_name`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_crop_categories_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_crop_categories_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_crop_categories`
--

LOCK TABLES `master_crop_categories` WRITE;
/*!40000 ALTER TABLE `master_crop_categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `master_crop_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_crops`
--

DROP TABLE IF EXISTS `master_crops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_crops` (
  `id` int NOT NULL AUTO_INCREMENT,
  `crop_name` varchar(100) NOT NULL,
  `description` text,
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_crops_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_crops_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_crops`
--

LOCK TABLES `master_crops` WRITE;
/*!40000 ALTER TABLE `master_crops` DISABLE KEYS */;
INSERT INTO `master_crops` VALUES (1,'Wheat','Winter wheat variety','/uploads/crops/crop-1743315629411-600342337.jpg',1,1,1,'2024-12-15 12:02:20','2025-03-30 06:20:29'),(2,'Drumsticks','desc','/uploads/crops/crop-1744947200154-289326447.jpg',1,1,1,'2025-02-23 04:29:09','2025-04-18 03:33:22'),(3,'Rice','desc','/uploads/crops/crop-1744947211018-418974952.jpg',1,1,1,'2025-02-23 04:29:27','2025-04-18 03:33:31'),(4,'Banana','desc','/uploads/crops/crop-1744946957285-514442013.jpg',1,1,1,'2025-02-25 12:15:15','2025-04-18 03:29:17'),(5,'Apple Tomatoes','this is the description for tomatoes','/uploads/crops/crop-1744946945655-923669000.jpg',1,1,1,'2025-03-30 05:02:12','2025-04-18 03:29:05'),(6,'Turmeric','turmeric','/uploads/crops/crop-1744991873602-401254974.jpg',1,1,NULL,'2025-04-18 15:57:53','2025-04-18 15:57:53');
/*!40000 ALTER TABLE `master_crops` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_equipment_types`
--

DROP TABLE IF EXISTS `master_equipment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_equipment_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_equipment_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_equipment_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_equipment_types`
--

LOCK TABLES `master_equipment_types` WRITE;
/*!40000 ALTER TABLE `master_equipment_types` DISABLE KEYS */;
INSERT INTO `master_equipment_types` VALUES (1,'Tractor','Farm vehicle for pulling heavy equipment',1,1,NULL,'2024-12-23 17:21:27','2024-12-23 17:21:27'),(2,'Harvester','Machine for harvesting crops',1,1,NULL,'2024-12-23 17:21:43','2024-12-23 17:21:43'),(3,'Plougher','test description',1,1,NULL,'2025-02-23 05:11:17','2025-02-23 05:11:17');
/*!40000 ALTER TABLE `master_equipment_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_farming_types`
--

DROP TABLE IF EXISTS `master_farming_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_farming_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_farming_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_farming_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_farming_types`
--

LOCK TABLES `master_farming_types` WRITE;
/*!40000 ALTER TABLE `master_farming_types` DISABLE KEYS */;
INSERT INTO `master_farming_types` VALUES (1,'Organic Farming','Farming without use of synthetic chemicals',1,1,NULL,'2024-12-15 10:23:52','2024-12-15 10:23:52'),(2,'Natural Farming','Farming with the use of natural methods',1,1,1,'2024-12-15 10:26:26','2025-02-22 18:30:11'),(3,'test farming type','this is the test farming type',1,1,NULL,'2025-04-13 06:31:04','2025-04-13 06:31:04');
/*!40000 ALTER TABLE `master_farming_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_irrigation_types`
--

DROP TABLE IF EXISTS `master_irrigation_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_irrigation_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_irrigation_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_irrigation_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_irrigation_types`
--

LOCK TABLES `master_irrigation_types` WRITE;
/*!40000 ALTER TABLE `master_irrigation_types` DISABLE KEYS */;
INSERT INTO `master_irrigation_types` VALUES (1,'Drip Irrigation','Water-efficient method that delivers water directly to plant roots',1,1,NULL,'2024-12-23 17:23:34','2024-12-23 17:23:34'),(2,'Center Pivot Irrigation','Mechanized sprinkler system that rotates around a pivot',1,1,NULL,'2024-12-23 17:23:47','2024-12-23 17:23:47');
/*!40000 ALTER TABLE `master_irrigation_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_livestock_types`
--

DROP TABLE IF EXISTS `master_livestock_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_livestock_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_livestock_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_livestock_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_livestock_types`
--

LOCK TABLES `master_livestock_types` WRITE;
/*!40000 ALTER TABLE `master_livestock_types` DISABLE KEYS */;
INSERT INTO `master_livestock_types` VALUES (1,'Cat','All types of cows and bulls',1,1,1,'2024-12-23 17:17:59','2025-02-19 03:57:13'),(2,'Dairy Cattle','Milk producing cows and bulls',1,1,NULL,'2024-12-23 17:19:31','2024-12-23 17:19:31'),(3,'test live stock','Desc',0,1,1,'2025-02-18 18:11:24','2025-02-18 18:11:28'),(4,'Poultry','Hens, Countryrange and Broiler',1,1,NULL,'2025-02-18 18:12:10','2025-02-18 18:12:10');
/*!40000 ALTER TABLE `master_livestock_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `master_states`
--

DROP TABLE IF EXISTS `master_states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_states` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state_name` varchar(100) NOT NULL,
  `state_code` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `master_states_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `master_states_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_states`
--

LOCK TABLES `master_states` WRITE;
/*!40000 ALTER TABLE `master_states` DISABLE KEYS */;
INSERT INTO `master_states` VALUES (1,'Maharashtra','MH',1,1,1,'2024-12-15 07:10:38','2025-02-22 16:48:43'),(2,'Tamil Nadu','TN',1,1,NULL,'2024-12-15 07:14:05','2024-12-15 07:14:05');
/*!40000 ALTER TABLE `master_states` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `queries`
--

DROP TABLE IF EXISTS `queries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `queries` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `image_url` varchar(255) DEFAULT NULL,
  `status` enum('open','in_progress','resolved','closed') DEFAULT 'open',
  `farmer_id` int NOT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `farmer_id` (`farmer_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `queries_ibfk_1` FOREIGN KEY (`farmer_id`) REFERENCES `farmers` (`id`),
  CONSTRAINT `queries_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `farmers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `queries`
--

LOCK TABLES `queries` WRITE;
/*!40000 ALTER TABLE `queries` DISABLE KEYS */;
INSERT INTO `queries` VALUES (3,'பருத்தி பயிரில் பூச்சி தாக்குதல்','என் பருத்தி இலைகளில் கருப்பு புள்ளிகள் காணப்படுகின்றன. உடனடி உதவி தேவை.',NULL,'closed',8,NULL,8,'2025-02-03 17:41:56'),(4,'Soil Quality Check Required','My paddy field shows uneven growth. Some areas have yellowing plants. Need soil testing advice.','https://example.com/images/soil-test.jpg','closed',8,NULL,8,'2025-02-03 17:47:31'),(5,'Yellow spots on wheat leaves','I\'ve noticed yellow spots on my wheat crop leaves. What could be causing this and how can I treat it?','/uploads/query-images/query-1740937033646-265603198.jpg','closed',23,NULL,23,'2025-03-02 17:37:13'),(6,'Water issues in our area','This is a test description explaining about the water issues being faced in our area. is there a solution to it ? if so kindly help out with what is to be done.','/uploads/query-images/query-1741326923518-936244481.jpg','resolved',23,NULL,23,'2025-03-07 05:55:23'),(7,'ertertertertert','erertertert','/uploads/query-images/query-1741329309747-747494929.jpg','in_progress',23,NULL,23,'2025-03-07 06:35:09'),(8,'pest issue','thi sis the description','/uploads/query-images/query-1744909279932-136849623.jpg','resolved',23,NULL,23,'2025-04-17 17:01:19'),(9,'My Drip irrigation failed','Drip irrigation is not working, can you help with it','/uploads/query-images/query-1744944953100-629367109.jpg','resolved',25,NULL,25,'2025-04-18 02:55:53'),(10,'test issue','test','/uploads/query-images/query-1744966988936-916899500.jpg','open',23,NULL,23,'2025-04-18 09:03:08'),(11,'Farmers query','Farmers description','/uploads/query-images/query-1744991616751-406037435.jpg','resolved',23,NULL,23,'2025-04-18 15:53:36'),(12,'issue in plant leves - Moringa','this is the description','/uploads/query-images/query-1745229811218-104509110.jpg','open',24,NULL,24,'2025-04-21 10:03:31');
/*!40000 ALTER TABLE `queries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `query_responses`
--

DROP TABLE IF EXISTS `query_responses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `query_responses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `query_id` int NOT NULL,
  `response` text,
  `image_url` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `query_id` (`query_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `query_responses_ibfk_1` FOREIGN KEY (`query_id`) REFERENCES `queries` (`id`),
  CONSTRAINT `query_responses_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `query_responses_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `query_responses`
--

LOCK TABLES `query_responses` WRITE;
/*!40000 ALTER TABLE `query_responses` DISABLE KEYS */;
INSERT INTO `query_responses` VALUES (1,4,'resonse',NULL,1,NULL,'2025-02-25 12:20:14','2025-02-25 12:20:14'),(2,4,'test response','/uploads/articles/image-1740499221786-240209834.jpg',1,NULL,'2025-02-25 16:00:21','2025-02-25 16:00:21'),(3,5,'this is a test response','/uploads/articles/image-1741326695699-630676912.png',1,NULL,'2025-03-07 05:51:35','2025-03-07 05:51:35'),(4,5,'This is another response',NULL,1,NULL,'2025-03-07 05:51:58','2025-03-07 05:51:58'),(5,6,'This is a post explaining what to do about the water issues in your area. This is a test description that is going to be used as a place holder.',NULL,1,NULL,'2025-03-07 05:56:55','2025-03-07 05:56:55'),(6,7,'this is the resolutiojs',NULL,1,NULL,'2025-03-07 06:35:41','2025-03-07 06:35:41'),(7,7,'this is the issue no worried.',NULL,1,NULL,'2025-04-04 10:02:15','2025-04-04 10:02:15'),(8,8,'test test','/uploads/articles/image-1744909341915-37283048.jpg',1,NULL,'2025-04-17 17:02:21','2025-04-17 17:02:21'),(9,9,'We\'ll send a person to repair. his name is Jeeva.','/uploads/articles/image-1744944992763-787790409.jpg',1,NULL,'2025-04-18 02:56:32','2025-04-18 02:56:32'),(10,11,'this is the response from the fpo','/uploads/articles/image-1744991656872-800722135.jpg',1,NULL,'2025-04-18 15:54:16','2025-04-18 15:54:16');
/*!40000 ALTER TABLE `query_responses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int DEFAULT NULL,
  `module` varchar(50) DEFAULT NULL,
  `permissions` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1,'farmers','[\"create\", \"read\", \"update\", \"delete\"]'),(2,1,'users','[\"create\", \"read\", \"update\", \"delete\"]'),(3,1,'roles','[\"create\", \"read\", \"update\", \"delete\", \"assign\"]'),(6,1,'farmers','[\"create\", \"read\", \"update\", \"delete\"]'),(7,3,'farmers','[\"read\"]'),(8,3,'users','[\"read\"]'),(9,1,'masters','[\"read\", \"create\", \"update\", \"delete\"]');
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_name` (`role_name`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `roles_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `roles_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'superadmin','2024-11-21 18:01:54','2024-11-21 18:01:54',NULL,NULL),(3,'field_manager','2024-11-24 16:37:50','2024-11-24 16:37:50',1,NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_creators`
--

DROP TABLE IF EXISTS `transaction_creators`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_creators` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_id` int NOT NULL,
  `creator_type` enum('user','farmer') NOT NULL,
  `creator_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  CONSTRAINT `transaction_creators_ibfk_1` FOREIGN KEY (`transaction_id`) REFERENCES `marketplace_transactions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_creators`
--

LOCK TABLES `transaction_creators` WRITE;
/*!40000 ALTER TABLE `transaction_creators` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_creators` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `user_id` int NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1),(4,3),(6,3),(8,3);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `mobile_number` varchar(15) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `mobile_number` (`mobile_number`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'kalsodigital','$2a$10$46DtLSk2odc1xiBmox7YRequ/lOrKSIdos4xAsp.nRYXs0CAdYZWW','<EMAIL>',NULL,'Gokul',1,'2024-11-21 18:04:28','2024-12-23 16:45:06',NULL,NULL),(4,'fieldmanager1','$2a$10$GgTM9xsehJnt7XWd3oG6oemT.fQ4eYhlQjmHbBmVdgpVHQnVBy/P6','<EMAIL>','9876543210','Field Manager One',1,'2024-11-24 16:45:37','2024-11-24 16:45:37',1,NULL),(6,'field_manager3','$2a$10$lSxiOVBQ.iq.LJQdRFVhCOpyjBhURB8Ti8WUKbfW6H7VoYu8YcIwC','<EMAIL>','9876543212','Field Manager Two',1,'2024-12-07 15:52:34','2024-12-07 15:52:34',1,NULL),(8,'field_manager4','$2a$10$63yt/TKU2bW/gTDoHbz/G.tpgKB.alulh77UbKtOht/Mq1GBYwbw2','<EMAIL>','9876543214','Field Manager Two',1,'2024-12-09 17:16:47','2024-12-09 17:16:47',1,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-27  9:30:56
