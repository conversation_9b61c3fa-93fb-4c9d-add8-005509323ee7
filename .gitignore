# Flutter/Dart specific
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
.flutter-plugins-dependencies
.flutter-plugins
.metadata
*.flutter-plugins
*.flutter-plugins-dependencies

# Generated Dart Files
*.g.dart
*.freezed.dart
*.mocks.dart
*.config.dart
*.gr.dart

# Android specific
/android/app/debug.keystore
/android/app/profile
/android/app/release
/android/gradle/wrapper/gradle-wrapper.jar
/android/.gradle
/android/captures/
/android/gradlew
/android/gradlew.bat
/android/local.properties
/android/*.iml
/android/app/debug
/android/app/profile
/android/app/release
android/key.properties
*.jks

# iOS specific
/ios/*.mode1v3
/ios/*.mode2v3
/ios/*.moved-aside
/ios/*.pbxuser
/ios/*.perspectivev3
/ios/**/*sync/
/ios/**/.sconsign.dblite
/ios/**/.tags*
/ios/**/.vagrant/
/ios/**/DerivedData/
/ios/**/Icon?
/ios/**/Pods/
/ios/**/profile
/ios/**/xcuserdata
/ios/.generated/
/ios/Flutter/.last_build_id
/ios/Flutter/App.framework
/ios/Flutter/Flutter.framework
/ios/Flutter/Flutter.podspec
/ios/Flutter/Generated.xcconfig
/ios/Flutter/ephemeral
/ios/Flutter/app.flx
/ios/Flutter/app.zip
/ios/Flutter/flutter_assets/
/ios/Flutter/flutter_export_environment.sh
/ios/ServiceDefinitions.json
/ios/Runner/GeneratedPluginRegistrant.*
/ios/Flutter/Debug.xcconfig
/ios/Flutter/Release.xcconfig
/ios/Runner.xcworkspace/xcshareddata/
/ios/Runner.xcodeproj/xcshareddata/
/ios/Runner.xcworkspace/xcuserdata/
/ios/Runner.xcodeproj/xcuserdata/

# macOS specific
/macos/Flutter/ephemeral
/macos/Flutter/GeneratedPluginRegistrant.swift
/macos/Flutter/Flutter-Debug.xcconfig
/macos/Flutter/Flutter-Release.xcconfig
/macos/Flutter/Flutter-Generated.xcconfig

# Linux specific
/linux/flutter/ephemeral

# Windows specific
/windows/flutter/ephemeral

# Web specific
/web/packages
/web/plugins
/web/assets

# IDE specific files
.idea/
**/.idea/**
.vscode/
*.iml
*.iws
*.ipr
.classpath
.project
.settings/
.vscode/
*.sublime-workspace

# Platform-specific
.DS_Store
Thumbs.db
ehthumbs.db
$RECYCLE.BIN/
Desktop.ini

# Project specific
fpo_traders.zip

# Package files
pubspec.lock
.packages

# Large files and archives
*.zip
*.rar
*.gz
*.tar
*.7z
*.iso
*.dmg
*.apk
*.aab
*.ipa

# Firebase configuration files
google-services.json
GoogleService-Info.plist

# Keystore files
upload-keystore.jks
*.keystore
*.p8
*.p12
*.mobileprovision

# Logs and databases
*.log
*.sqlite
*.db

# Coverage reports
coverage/
.test_coverage.dart
lcov.info